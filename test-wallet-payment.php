<?php

// Teste específico para pagamento com carteira
require_once 'vendor/autoload.php';

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;

// Configurar Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testando pagamento com carteira para eventos...\n\n";

try {
    // 1. Encontrar um usuário para teste
    echo "1. Verificando usuário para teste...\n";
    $user = User::where('role', '!=', 'visitante')->first();
    
    if (!$user) {
        echo "   ❌ Nenhum usuário encontrado\n";
        exit(1);
    }
    
    echo "   ✅ Usuário encontrado: {$user->name} (ID: {$user->id})\n";

    // 2. Verificar/criar carteira
    echo "\n2. Verificando carteira do usuário...\n";
    $wallet = $user->wallet;
    
    if (!$wallet) {
        echo "   ⚠️  Criando carteira para o usuário...\n";
        $wallet = $user->wallet()->create([
            'balance' => 0.00,
            'active' => true,
        ]);
    }
    
    echo "   ✅ Carteira encontrada - Saldo atual: R$ " . number_format($wallet->balance, 2, ',', '.') . "\n";

    // 3. Encontrar evento pago
    echo "\n3. Verificando evento pago...\n";
    $event = Event::where('is_active', true)
        ->where('date', '>=', now()->toDateString())
        ->where('price', '>', 0)
        ->first();
    
    if (!$event) {
        echo "   ❌ Nenhum evento pago encontrado\n";
        exit(1);
    }
    
    echo "   ✅ Evento encontrado: {$event->name} - {$event->formatted_price}\n";

    // 4. Verificar se usuário já está inscrito
    echo "\n4. Verificando inscrição existente...\n";
    $existingRegistration = EventAttendee::where('event_id', $event->id)
        ->where('user_id', $user->id)
        ->where('status', '!=', 'cancelled')
        ->first();

    if ($existingRegistration) {
        echo "   ⚠️  Usuário já está inscrito - cancelando para teste...\n";
        $existingRegistration->update(['status' => 'cancelled']);
    } else {
        echo "   ✅ Usuário não está inscrito\n";
    }

    // 5. Verificar se há saldo suficiente
    echo "\n5. Verificando saldo da carteira...\n";
    echo "   - Saldo atual: R$ " . number_format($wallet->balance, 2, ',', '.') . "\n";
    echo "   - Preço do evento: {$event->formatted_price}\n";
    
    if ($wallet->balance < $event->price) {
        echo "   ⚠️  Saldo insuficiente - adicionando fundos para teste...\n";
        
        $amountToAdd = $event->price + 50; // Adicionar um pouco extra
        $transaction = $wallet->addFunds(
            $amountToAdd,
            'deposit',
            'Depósito para teste de pagamento de evento'
        );
        
        if ($transaction) {
            echo "   ✅ Fundos adicionados: R$ " . number_format($amountToAdd, 2, ',', '.') . "\n";
            echo "   ✅ Novo saldo: R$ " . number_format($wallet->balance, 2, ',', '.') . "\n";
        } else {
            echo "   ❌ Falha ao adicionar fundos\n";
            exit(1);
        }
    } else {
        echo "   ✅ Saldo suficiente para o evento\n";
    }

    // 6. Simular pagamento com carteira
    echo "\n6. Simulando pagamento com carteira...\n";
    
    // Criar inscrição
    $attendee = EventAttendee::create([
        'event_id' => $event->id,
        'user_id' => $user->id,
        'status' => 'confirmed',
        'ticket_code' => EventAttendee::generateTicketCode(),
        'payment_status' => 'completed',
        'payment_method' => 'wallet',
        'amount_paid' => $event->price,
        'paid_at' => now(),
    ]);

    echo "   ✅ Inscrição criada (ID: {$attendee->id})\n";
    echo "   🎫 Código do ingresso: {$attendee->ticket_code}\n";

    // Debitar da carteira
    $walletTransaction = $wallet->subtractFunds(
        $event->price,
        'purchase',
        "Ingresso para evento: {$event->name}",
        $attendee->id,
        'event_registration'
    );

    if ($walletTransaction) {
        echo "   ✅ Valor debitado da carteira\n";
        echo "   💰 Novo saldo: R$ " . number_format($wallet->balance, 2, ',', '.') . "\n";
        echo "   📝 Transação ID: {$walletTransaction->id}\n";
    } else {
        echo "   ❌ Falha ao debitar da carteira\n";
        $attendee->delete();
        exit(1);
    }

    // 7. Verificar dados finais
    echo "\n7. Verificando dados finais...\n";
    
    // Recarregar modelos
    $attendee->refresh();
    $wallet->refresh();
    
    echo "   - Status da inscrição: {$attendee->status}\n";
    echo "   - Status do pagamento: {$attendee->payment_status}\n";
    echo "   - Método de pagamento: {$attendee->payment_method}\n";
    echo "   - Valor pago: R$ " . number_format($attendee->amount_paid, 2, ',', '.') . "\n";
    echo "   - Saldo final da carteira: R$ " . number_format($wallet->balance, 2, ',', '.') . "\n";

    // 8. Verificar transação na carteira
    echo "\n8. Verificando transação na carteira...\n";
    $transaction = WalletTransaction::where('wallet_id', $wallet->id)
        ->where('reference_id', $attendee->id)
        ->where('reference_type', 'event_registration')
        ->first();

    if ($transaction) {
        echo "   ✅ Transação encontrada:\n";
        echo "      - ID: {$transaction->id}\n";
        echo "      - Tipo: {$transaction->type_text}\n";
        echo "      - Valor: {$transaction->formatted_amount}\n";
        echo "      - Saldo após: {$transaction->formatted_balance}\n";
        echo "      - Descrição: {$transaction->description}\n";
    } else {
        echo "   ❌ Transação não encontrada\n";
    }

    // 9. Limpar dados de teste
    echo "\n9. Limpando dados de teste...\n";
    $attendee->delete();
    if ($transaction) {
        $transaction->delete();
    }
    
    // Restaurar saldo original (subtrair o que foi adicionado)
    if (isset($amountToAdd)) {
        $wallet->subtractFunds(
            $amountToAdd,
            'withdrawal',
            'Remoção de fundos de teste'
        );
    }
    
    echo "   ✅ Dados de teste removidos\n";

    echo "\n✅ Teste de pagamento com carteira concluído com sucesso!\n";

    // 10. Mostrar instruções para teste manual
    echo "\n📋 Instruções para teste manual:\n";
    echo "   1. Acesse: /eventos/{$event->slug}\n";
    echo "   2. Certifique-se de ter saldo suficiente na carteira\n";
    echo "   3. Clique em 'Inscrever-se por {$event->formatted_price}'\n";
    echo "   4. No modal, selecione 'Carteira Digital'\n";
    echo "   5. Clique em 'Confirmar'\n";
    echo "   6. Verifique se o saldo foi debitado\n";
    echo "   7. Verifique a transação em /carteira\n";

} catch (Exception $e) {
    echo "\n❌ Erro durante o teste: {$e->getMessage()}\n";
    echo "📍 Arquivo: {$e->getFile()}:{$e->getLine()}\n";
    exit(1);
}

echo "\n🎉 Teste concluído!\n";
