-- SQL para verificar o estado do sistema de pagamento via carteira
-- Execute este script no phpMyAdmin em produção

-- 1. Verificar eventos ativos com preço
SELECT 
    id,
    name,
    date,
    price,
    capacity,
    is_active,
    created_at
FROM events 
WHERE is_active = 1 
AND price > 0
ORDER BY date ASC;

-- 2. Verificar usuários com carteiras
SELECT 
    u.id,
    u.name,
    u.email,
    u.role,
    w.balance,
    w.active as wallet_active,
    w.created_at as wallet_created
FROM users u
LEFT JOIN wallets w ON u.id = w.user_id
WHERE u.role != 'visitante'
ORDER BY w.balance DESC;

-- 3. Verificar inscrições pagas via carteira (últimos 30 dias)
SELECT 
    ea.id,
    ea.event_id,
    e.name as event_name,
    ea.user_id,
    u.name as user_name,
    ea.status,
    ea.payment_status,
    ea.payment_method,
    ea.amount_paid,
    ea.ticket_code,
    ea.paid_at,
    ea.created_at
FROM event_attendees ea
JOIN events e ON ea.event_id = e.id
JOIN users u ON ea.user_id = u.id
WHERE ea.payment_method = 'wallet'
AND ea.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY ea.created_at DESC;

-- 4. Verificar transações de carteira para eventos (últimos 30 dias)
SELECT 
    wt.id,
    wt.wallet_id,
    wt.user_id,
    u.name as user_name,
    wt.type,
    wt.amount,
    wt.balance_after,
    wt.status,
    wt.reference_id,
    wt.reference_type,
    wt.description,
    wt.created_at
FROM wallet_transactions wt
JOIN users u ON wt.user_id = u.id
WHERE wt.reference_type = 'event_registration'
AND wt.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY wt.created_at DESC;

-- 5. Verificar inconsistências - inscrições pagas sem transação correspondente
SELECT 
    ea.id as attendee_id,
    ea.event_id,
    e.name as event_name,
    ea.user_id,
    u.name as user_name,
    ea.amount_paid,
    ea.paid_at,
    'Sem transação correspondente' as issue
FROM event_attendees ea
JOIN events e ON ea.event_id = e.id
JOIN users u ON ea.user_id = u.id
LEFT JOIN wallet_transactions wt ON (
    wt.reference_id = ea.id 
    AND wt.reference_type = 'event_registration'
)
WHERE ea.payment_method = 'wallet'
AND ea.payment_status = 'completed'
AND wt.id IS NULL
ORDER BY ea.created_at DESC;

-- 6. Verificar transações órfãs - transações sem inscrição correspondente
SELECT 
    wt.id as transaction_id,
    wt.user_id,
    u.name as user_name,
    wt.amount,
    wt.reference_id,
    wt.description,
    wt.created_at,
    'Transação órfã' as issue
FROM wallet_transactions wt
JOIN users u ON wt.user_id = u.id
LEFT JOIN event_attendees ea ON (
    ea.id = wt.reference_id 
    AND wt.reference_type = 'event_registration'
)
WHERE wt.reference_type = 'event_registration'
AND ea.id IS NULL
ORDER BY wt.created_at DESC;

-- 7. Verificar saldos negativos (não deveria existir)
SELECT 
    w.id as wallet_id,
    w.user_id,
    u.name as user_name,
    w.balance,
    w.updated_at,
    'Saldo negativo' as issue
FROM wallets w
JOIN users u ON w.user_id = u.id
WHERE w.balance < 0;

-- 8. Resumo estatístico dos últimos 30 dias
SELECT 
    'Inscrições via carteira' as metric,
    COUNT(*) as count,
    SUM(amount_paid) as total_amount
FROM event_attendees 
WHERE payment_method = 'wallet'
AND payment_status = 'completed'
AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)

UNION ALL

SELECT 
    'Transações de eventos' as metric,
    COUNT(*) as count,
    SUM(ABS(amount)) as total_amount
FROM wallet_transactions 
WHERE reference_type = 'event_registration'
AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)

UNION ALL

SELECT 
    'Usuários com carteira' as metric,
    COUNT(*) as count,
    SUM(balance) as total_amount
FROM wallets 
WHERE active = 1;

-- 9. Verificar eventos com maior número de inscrições via carteira
SELECT 
    e.id,
    e.name,
    e.date,
    e.price,
    COUNT(ea.id) as wallet_registrations,
    SUM(ea.amount_paid) as total_revenue
FROM events e
JOIN event_attendees ea ON e.id = ea.event_id
WHERE ea.payment_method = 'wallet'
AND ea.payment_status = 'completed'
GROUP BY e.id, e.name, e.date, e.price
ORDER BY wallet_registrations DESC;

-- 10. Verificar últimas atividades de carteira
SELECT 
    wt.id,
    wt.user_id,
    u.name as user_name,
    wt.type,
    wt.amount,
    wt.balance_after,
    wt.description,
    wt.created_at
FROM wallet_transactions wt
JOIN users u ON wt.user_id = u.id
ORDER BY wt.created_at DESC
LIMIT 20;
