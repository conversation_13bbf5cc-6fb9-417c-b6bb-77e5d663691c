<div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 p-4">
    <div class="max-w-7xl mx-auto">
        <!-- Header do Plano -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6 mb-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-title mb-2">{{ $plan->title ?? 'Plano sem título' }}</h1>
                    @if($plan->description)
                        <p class="text-body mb-4">{{ $plan->description }}</p>
                    @endif

                    <div class="flex items-center space-x-6 text-sm text-body">
                        <div class="flex items-center space-x-2">
                            <flux:icon name="user" class="w-4 h-4" />
                            <span>Criado por <strong>{{ $plan->user->name ?? 'Usuário desconhecido' }}</strong></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <flux:icon name="calendar" class="w-4 h-4" />
                            <span>{{ $plan->created_at ? $plan->created_at->format('d/m/Y') : 'Data não disponível' }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <flux:icon name="currency-dollar" class="w-4 h-4 text-green-500" />
                            <span class="text-green-500 font-semibold">R$ {{ number_format($plan->total_support_amount ?? 0, 2, ',', '.') }} arrecadados</span>
                        </div>
                    </div>
                </div>

                @if(auth()->id() === $plan->user_id)
                    <flux:button variant="ghost" size="sm">
                        <flux:icon name="pencil" class="w-4 h-4 mr-2" />
                        Editar
                    </flux:button>
                @endif
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Legenda dos Blocos -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4 mb-6">
                    <h3 class="text-lg font-semibold text-title mb-4">Legenda</h3>
                    <div class="space-y-3">
                        @foreach($this->blockTypes as $type => $block)
                            @if(is_array($block) && isset($block['name'], $block['icon'], $block['color']))
                            <div class="flex items-center space-x-3">
                                <flux:icon
                                    name="{{ $block['icon'] }}"
                                    class="w-5 h-5"
                                    style="color: {{ $block['color'] }}; filter: drop-shadow(0 0 3px {{ $block['color'] }}50);"
                                />
                                <span class="text-sm text-title">{{ $block['name'] }}</span>
                            </div>
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Patrocínios Recentes -->
                @if($plan->supports->count() > 0)
                    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4">
                        <h3 class="text-lg font-semibold text-title mb-4">Patrocínios Recentes</h3>
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            @foreach($plan->supports->take(5) as $support)
                                <div class="flex items-center space-x-3 p-2 bg-zinc-50 dark:bg-zinc-700 rounded-lg">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                        <flux:icon name="currency-dollar" class="w-4 h-4 text-white" />
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-title truncate">{{ $support->user->name ?? 'Usuário desconhecido' }}</p>
                                        <p class="text-xs text-body">{{ $support->position ?? '' }} - {{ $support->formatted_amount ?? 'R$ 0,00' }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Tabuleiro -->
            <div class="lg:col-span-3">
                <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                    <h3 class="text-lg font-semibold text-title mb-4">Plano da Noite</h3>
                    <p class="text-sm text-body mb-4">
                        <span class="font-medium">Casas Pretas:</span> Experiências planejadas |
                        <span class="font-medium">Casas Brancas:</span> Clique para patrocinar
                    </p>

                    <div class="grid grid-cols-8 gap-1 max-w-2xl mx-auto">
                        @if(is_array($plan->board))
                        @foreach(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] as $row)
                            @for($col = 1; $col <= 8; $col++)
                                @php
                                    $position = (string) ($row . $col);
                                    $square = $plan->board[$position] ?? ['type' => null, 'is_black' => false];
                                    $isBlack = $this->isBlackSquare($position);
                                    $support = $this->getSupportAtPosition($position);
                                @endphp

                                <div
                                    @if(!$isBlack && !$support && auth()->id() !== $plan->user_id)
                                        wire:click="openSponsorModal('{{ $position }}')"
                                    @endif
                                    class="chess-square aspect-square border border-zinc-300 dark:border-zinc-600 rounded-lg relative {{ $isBlack ? 'bg-zinc-800 dark:bg-zinc-900' : 'bg-zinc-100 dark:bg-zinc-200' }} {{ !$isBlack && !$support && auth()->id() !== $plan->user_id ? 'sponsor-available' : '' }} {{ $support ? 'sponsored-square' : '' }}"
                                >
                                    <!-- Posição -->
                                    <div class="absolute top-1 left-1 text-xs font-mono {{ $isBlack ? 'text-zinc-400' : 'text-zinc-600' }}">
                                        {{ $position }}
                                    </div>

                                    <!-- Conteúdo da casa -->
                                    <div class="flex items-center justify-center h-full">
                                        @if(isset($square['type']) && $square['type'] && $isBlack && isset($this->blockTypes[$square['type']]))
                                            <!-- Bloco temático -->
                                            <div class="text-center">
                                                <flux:icon
                                                    name="{{ $this->blockTypes[$square['type']]['icon'] }}"
                                                    class="block-icon w-6 h-6 mx-auto mb-1"
                                                    style="color: {{ $this->blockTypes[$square['type']]['color'] }};"
                                                />
                                                <div class="text-xs text-zinc-300 font-medium">
                                                    {{ $this->blockTypes[$square['type']]['name'] }}
                                                </div>
                                            </div>
                                        @elseif(!$isBlack)
                                            @if($support)
                                                <!-- Casa patrocinada -->
                                                <div class="text-center">
                                                    <div class="w-6 h-6 bg-green-500 rounded-full mx-auto mb-1 flex items-center justify-center">
                                                        <flux:icon name="currency-dollar" class="w-3 h-3 text-white" />
                                                    </div>
                                                    <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                                                        {{ $support->formatted_amount ?? 'R$ 0,00' }}
                                                    </div>
                                                </div>
                                            @elseif(auth()->id() !== $plan->user_id)
                                                <!-- Casa disponível para patrocínio -->
                                                <div class="text-center opacity-50 hover:opacity-100 transition-opacity">
                                                    <flux:icon name="plus" class="w-6 h-6 mx-auto text-green-500" />
                                                    <div class="text-xs text-green-600 dark:text-green-400 font-medium">
                                                        Patrocinar
                                                    </div>
                                                </div>
                                            @endif
                                        @endif
                                    </div>

                                    <!-- Tooltip para patrocínio -->
                                    @if($support)
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 text-white text-xs rounded opacity-0 hover:opacity-100 transition-opacity pointer-events-none z-10">
                                            <div class="font-medium">{{ $support->user->name ?? 'Usuário desconhecido' }}</div>
                                            @if($support->comment)
                                                <div class="text-zinc-300">{{ $support->comment }}</div>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            @endfor
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Patrocínio -->
    <flux:modal wire:model="showSponsorModal" class="max-w-md">
        <flux:modal.header>
            <flux:heading size="lg">Patrocinar Posição {{ $selectedPosition ?? '' }}</flux:heading>
        </flux:modal.header>

        <flux:modal.body>
            <div class="space-y-4">
                <div class="text-center p-4 bg-zinc-50 dark:bg-zinc-700 rounded-lg">
                    <p class="text-sm text-body">
                        Você está patrocinando a posição <strong>{{ $selectedPosition ?? '' }}</strong> no plano
                        "<strong>{{ $plan->title ?? '' }}</strong>" de <strong>{{ $plan->user->name ?? '' }}</strong>
                    </p>
                </div>

                <flux:field>
                    <flux:label>Valor do Patrocínio (R$)</flux:label>
                    <flux:input
                        wire:model="sponsorAmount"
                        type="number"
                        step="0.01"
                        min="1"
                        max="1000"
                        placeholder="Ex: 25.00"
                    />
                    <flux:error name="sponsorAmount" />
                    <flux:description>
                        Saldo disponível: R$ {{ number_format(auth()->user()->wallet->balance ?? 0, 2, ',', '.') }}
                    </flux:description>
                </flux:field>

                <flux:field>
                    <flux:label>Comentário (opcional)</flux:label>
                    <flux:textarea
                        wire:model="sponsorComment"
                        placeholder="Deixe uma mensagem para o criador do plano..."
                        rows="3"
                    />
                    <flux:error name="sponsorComment" />
                </flux:field>
            </div>
        </flux:modal.body>

        <flux:modal.footer>
            <flux:button wire:click="$set('showSponsorModal', false)" variant="ghost">
                Cancelar
            </flux:button>
            <flux:button wire:click="sponsorPosition" variant="primary" class="night-board-btn neon-box-green">
                <flux:icon name="currency-dollar" class="w-4 h-4 mr-2" />
                Confirmar Patrocínio
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
</div>
