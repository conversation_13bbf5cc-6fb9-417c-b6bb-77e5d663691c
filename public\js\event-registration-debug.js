// Debug script for event registration
console.log('[Event Registration Debug] Script loaded');

document.addEventListener('DOMContentLoaded', function() {
    console.log('[Event Registration Debug] DOM loaded');
    
    // Check if we're on an event page
    if (!window.location.pathname.includes('/eventos/')) {
        console.log('[Event Registration Debug] Not on event page, skipping');
        return;
    }
    
    console.log('[Event Registration Debug] On event page, initializing debug');
    
    // Monitor Livewire initialization
    document.addEventListener('livewire:initialized', () => {
        console.log('[Event Registration Debug] Livewire initialized');
        
        // Find the event registration component
        const registrationComponent = document.querySelector('[wire\\:id]');
        if (registrationComponent) {
            const componentId = registrationComponent.getAttribute('wire:id');
            console.log('[Event Registration Debug] Found registration component:', componentId);
            
            // Monitor all Livewire events
            window.Livewire.hook('message.sent', (message, component) => {
                if (component.id === componentId) {
                    console.log('[Event Registration Debug] Message sent:', message);
                }
            });
            
            window.Livewire.hook('message.failed', (message, component) => {
                if (component.id === componentId) {
                    console.error('[Event Registration Debug] Message failed:', message);
                }
            });
            
            window.Livewire.hook('message.received', (message, component) => {
                if (component.id === componentId) {
                    console.log('[Event Registration Debug] Message received:', message);
                }
            });
            
            window.Livewire.hook('message.processed', (message, component) => {
                if (component.id === componentId) {
                    console.log('[Event Registration Debug] Message processed:', message);
                }
            });
            
            // Monitor component updates
            window.Livewire.hook('component.updated', (component) => {
                if (component.id === componentId) {
                    console.log('[Event Registration Debug] Component updated:', component);
                }
            });
            
            // Monitor redirects
            window.Livewire.hook('redirect', (url) => {
                console.log('[Event Registration Debug] Redirect to:', url);
            });
            
        } else {
            console.warn('[Event Registration Debug] Registration component not found');
        }
        
        // Monitor button clicks
        document.addEventListener('click', function(e) {
            if (e.target.matches('[wire\\:click*="confirmRegistration"]')) {
                console.log('[Event Registration Debug] Confirm registration button clicked');
            }
            
            if (e.target.matches('[wire\\:click*="register"]')) {
                console.log('[Event Registration Debug] Register button clicked');
            }
            
            if (e.target.matches('[wire\\:click*="cancelRegistration"]')) {
                console.log('[Event Registration Debug] Cancel registration button clicked');
            }
        });
        
        // Monitor form submissions
        document.addEventListener('submit', function(e) {
            if (e.target.closest('[wire\\:id]')) {
                console.log('[Event Registration Debug] Form submitted:', e.target);
            }
        });
        
        // Monitor modal state changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const target = mutation.target;
                    if (target.matches('[x-show]') || target.matches('.modal')) {
                        const isVisible = target.style.display !== 'none' && 
                                        !target.hasAttribute('hidden') &&
                                        target.offsetParent !== null;
                        console.log('[Event Registration Debug] Modal visibility changed:', {
                            element: target,
                            visible: isVisible
                        });
                    }
                }
            });
        });
        
        // Observe modal elements
        document.querySelectorAll('[x-show], .modal').forEach(function(modal) {
            observer.observe(modal, { attributes: true, attributeFilter: ['style', 'hidden'] });
        });
        
    });
    
    // Monitor Alpine.js events
    document.addEventListener('alpine:init', () => {
        console.log('[Event Registration Debug] Alpine.js initialized');
    });
    
    // Monitor network requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && url.includes('livewire')) {
            console.log('[Event Registration Debug] Livewire request:', url);
        }
        return originalFetch.apply(this, args)
            .then(response => {
                if (typeof url === 'string' && url.includes('livewire')) {
                    console.log('[Event Registration Debug] Livewire response:', response);
                }
                return response;
            })
            .catch(error => {
                if (typeof url === 'string' && url.includes('livewire')) {
                    console.error('[Event Registration Debug] Livewire error:', error);
                }
                throw error;
            });
    };
    
    // Monitor CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        console.log('[Event Registration Debug] CSRF token found:', csrfToken.getAttribute('content').substring(0, 10) + '...');
    } else {
        console.warn('[Event Registration Debug] CSRF token not found');
    }
    
    // Monitor session flash messages
    const flashMessages = document.querySelectorAll('[x-data*="flash"], .alert, .notification');
    if (flashMessages.length > 0) {
        console.log('[Event Registration Debug] Flash messages found:', flashMessages.length);
        flashMessages.forEach((msg, index) => {
            console.log(`[Event Registration Debug] Flash message ${index}:`, msg.textContent.trim());
        });
    }
    
    // Check for JavaScript errors
    window.addEventListener('error', function(e) {
        console.error('[Event Registration Debug] JavaScript error:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno,
            error: e.error
        });
    });
    
    // Check for unhandled promise rejections
    window.addEventListener('unhandledrejection', function(e) {
        console.error('[Event Registration Debug] Unhandled promise rejection:', e.reason);
    });
    
    // Utility function to test registration manually
    window.debugEventRegistration = function() {
        console.log('[Event Registration Debug] Manual test triggered');
        
        const component = window.Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id'));
        if (component) {
            console.log('[Event Registration Debug] Component state:', component.get());
            
            // Try to trigger registration
            try {
                component.call('confirmRegistration');
                console.log('[Event Registration Debug] confirmRegistration called');
            } catch (error) {
                console.error('[Event Registration Debug] Error calling confirmRegistration:', error);
            }
        } else {
            console.error('[Event Registration Debug] Component not found');
        }
    };
    
    console.log('[Event Registration Debug] Debug setup complete. Use debugEventRegistration() to test manually.');
});
