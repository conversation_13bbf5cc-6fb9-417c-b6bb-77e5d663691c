<?php

namespace App\Livewire\Events;

use App\Models\Event;
use App\Models\EventAttendee;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;

class EventRegistration extends Component
{
    public Event $event;
    public $isRegistered = false;
    public $attendee = null;
    public $showConfirmModal = false;
    public $showCancelModal = false;
    public $paymentMethod = 'stripe'; // 'stripe' or 'wallet'
    public $userWalletBalance = 0;

    public function mount(Event $event)
    {
        $this->event = $event;
        $this->checkRegistration();
        $this->loadWalletBalance();
    }

    public function render()
    {
        return view('livewire.events.event-registration');
    }

    public function checkRegistration()
    {
        if (Auth::check()) {
            $attendee = EventAttendee::where('event_id', $this->event->id)
                ->where('user_id', Auth::id())
                ->where('status', '!=', 'cancelled')
                ->first();

            $this->isRegistered = (bool) $attendee;
            $this->attendee = $attendee;
        } else {
            $this->isRegistered = false;
            $this->attendee = null;
        }
    }

    public function loadWalletBalance()
    {
        if (Auth::check()) {
            $user = Auth::user();

            // Recarregar o relacionamento da carteira para garantir dados atualizados
            $user->load('wallet');

            if ($user->wallet) {
                // Recarregar o modelo da carteira do banco de dados
                $user->wallet->refresh();
                $this->userWalletBalance = $user->wallet->balance;

                \Log::debug('EventRegistration: Wallet balance loaded', [
                    'user_id' => $user->id,
                    'wallet_id' => $user->wallet->id,
                    'balance' => $this->userWalletBalance
                ]);
            } else {
                $this->userWalletBalance = 0;

                \Log::warning('EventRegistration: User has no wallet', [
                    'user_id' => $user->id
                ]);
            }
        } else {
            $this->userWalletBalance = 0;
        }
    }

    public function confirmRegistration()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Check if the event is active
        if (!$this->event->is_active) {
            session()->flash('error', 'Este evento não está mais disponível para inscrições.');
            return;
        }

        // Check if the event is in the past
        if ($this->event->has_passed) {
            session()->flash('error', 'Este evento já ocorreu.');
            return;
        }

        // Check if the event is sold out
        if ($this->event->is_sold_out) {
            session()->flash('error', 'Este evento está esgotado.');
            return;
        }

        // Check if the user is already registered
        if ($this->isRegistered) {
            session()->flash('info', 'Você já está inscrito neste evento.');
            return;
        }

        $this->showConfirmModal = true;
    }

    public function register()
    {
        \Log::info('EventRegistration: register() called', [
            'event_id' => $this->event->id,
            'event_name' => $this->event->name,
            'event_price' => $this->event->price,
            'is_free' => $this->event->is_free,
            'user_id' => Auth::id()
        ]);

        if (!Auth::check()) {
            \Log::warning('EventRegistration: User not authenticated');
            return redirect()->route('login');
        }

        // Check if the event is active
        if (!$this->event->is_active) {
            \Log::warning('EventRegistration: Event not active', ['event_id' => $this->event->id]);
            session()->flash('error', 'Este evento não está mais disponível para inscrições.');
            return;
        }

        // Check if the event is in the past
        if ($this->event->has_passed) {
            \Log::warning('EventRegistration: Event has passed', ['event_id' => $this->event->id]);
            session()->flash('error', 'Este evento já ocorreu.');
            return;
        }

        // Check if the event is sold out
        if ($this->event->is_sold_out) {
            \Log::warning('EventRegistration: Event sold out', ['event_id' => $this->event->id]);
            session()->flash('error', 'Este evento está esgotado.');
            return;
        }

        // Check if the user is already registered
        $existingRegistration = EventAttendee::where('event_id', $this->event->id)
            ->where('user_id', Auth::id())
            ->where('status', '!=', 'cancelled')
            ->first();

        if ($existingRegistration) {
            \Log::info('EventRegistration: User already registered', [
                'event_id' => $this->event->id,
                'user_id' => Auth::id(),
                'attendee_id' => $existingRegistration->id
            ]);

            // Atualizar estado do componente
            $this->checkRegistration();

            session()->flash('info', 'Você já está inscrito neste evento.');
            $this->showConfirmModal = false;
            return;
        }

        // If the event is free, register the user directly
        if ($this->event->is_free) {
            try {
                $attendee = DB::transaction(function () {
                    // Verificar novamente dentro da transação
                    $existingRegistration = EventAttendee::where('event_id', $this->event->id)
                        ->where('user_id', Auth::id())
                        ->where('status', '!=', 'cancelled')
                        ->lockForUpdate()
                        ->first();

                    if ($existingRegistration) {
                        throw new \Exception('Você já está inscrito neste evento.');
                    }

                    try {
                        return EventAttendee::create([
                            'event_id' => $this->event->id,
                            'user_id' => Auth::id(),
                            'status' => 'confirmed',
                            'ticket_code' => EventAttendee::generateTicketCode(),
                            'payment_status' => 'completed',
                            'payment_method' => 'free',
                            'amount_paid' => 0,
                            'paid_at' => now(),
                        ]);
                    } catch (\Illuminate\Database\QueryException $e) {
                        if ($e->getCode() == 23000 || strpos($e->getMessage(), 'Duplicate entry') !== false) {
                            throw new \Exception('Você já está inscrito neste evento.');
                        }
                        throw $e;
                    }
                });

                $this->attendee = $attendee;
                $this->isRegistered = true;
                $this->showConfirmModal = false;

                session()->flash('success', 'Inscrição realizada com sucesso! Código: ' . $attendee->ticket_code);
                return;

            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'já está inscrito') !== false) {
                    \Log::info('EventRegistration: Free event - User already registered', [
                        'event_id' => $this->event->id,
                        'user_id' => Auth::id()
                    ]);

                    $this->checkRegistration();
                    session()->flash('info', $e->getMessage());
                    $this->showConfirmModal = false;
                    return;
                }

                \Log::error('Erro ao criar inscrição gratuita (Livewire): ' . $e->getMessage());
                session()->flash('error', 'Erro ao processar inscrição. Tente novamente.');
                $this->showConfirmModal = false;
                return;
            }
        }

        // If the event has a price, handle payment
        \Log::info('EventRegistration: Creating paid registration', [
            'event_id' => $this->event->id,
            'event_price' => $this->event->price,
            'payment_method' => $this->paymentMethod,
            'user_wallet_balance' => $this->userWalletBalance,
            'user_id' => Auth::id()
        ]);

        // Check if paying with wallet
        if ($this->paymentMethod === 'wallet') {
            return $this->processWalletPayment();
        }

        try {
            $attendee = EventAttendee::create([
                'event_id' => $this->event->id,
                'user_id' => Auth::id(),
                'status' => 'registered',
                'payment_status' => 'pending',
            ]);

            \Log::info('EventRegistration: Pending attendee created', ['attendee_id' => $attendee->id]);

            // Create Stripe checkout session
            Stripe::setApiKey(config('cashier.secret'));

            $sessionData = [
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'brl',
                        'product_data' => [
                            'name' => $this->event->name,
                            'description' => "Ingresso para {$this->event->name} em {$this->event->formatted_date}",
                            'images' => [$this->event->image_url],
                        ],
                        'unit_amount' => (int)($this->event->price * 100), // Convert to cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => route('events.payment.success', [
                    'event' => $this->event->id,
                    'attendee' => $attendee->id,
                    'session_id' => '{CHECKOUT_SESSION_ID}'
                ]),
                'cancel_url' => route('events.payment.cancel', [
                    'event' => $this->event->id,
                    'attendee' => $attendee->id
                ]),
                'customer_email' => Auth::user()->email,
                'metadata' => [
                    'event_id' => $this->event->id,
                    'attendee_id' => $attendee->id,
                    'user_id' => Auth::id(),
                ],
            ];

            \Log::info('EventRegistration: Creating Stripe session', [
                'amount' => $sessionData['line_items'][0]['price_data']['unit_amount'],
                'customer_email' => $sessionData['customer_email']
            ]);

            $session = Session::create($sessionData);

            \Log::info('EventRegistration: Stripe session created', [
                'session_id' => $session->id,
                'session_url' => $session->url
            ]);

            // Store session ID for verification
            $attendee->update(['payment_id' => $session->id]);

            $this->showConfirmModal = false;

            \Log::info('EventRegistration: Redirecting to Stripe', ['url' => $session->url]);
            return redirect($session->url);

        } catch (ApiErrorException $e) {
            // Delete the registration if payment creation fails
            if (isset($attendee)) {
                $attendee->delete();
            }

            \Log::error('Erro ao criar sessão Stripe (Livewire): ' . $e->getMessage());
            session()->flash('error', 'Ocorreu um erro ao processar o pagamento. Por favor, tente novamente.');
            $this->showConfirmModal = false;
        } catch (\Exception $e) {
            // Delete the registration if any other error occurs
            if (isset($attendee)) {
                $attendee->delete();
            }

            \Log::error('Erro geral ao processar inscrição (Livewire): ' . $e->getMessage());
            session()->flash('error', 'Erro interno. Tente novamente em alguns minutos.');
            $this->showConfirmModal = false;
        }
    }

    public function confirmCancel()
    {
        if (!$this->isRegistered || !$this->attendee) {
            session()->flash('error', 'Você não está inscrito neste evento.');
            return;
        }

        $this->showCancelModal = true;
    }

    public function cancelRegistration()
    {
        if (!$this->isRegistered || !$this->attendee) {
            session()->flash('error', 'Você não está inscrito neste evento.');
            return;
        }

        $this->attendee->cancel();

        $this->isRegistered = false;
        $this->attendee = null;
        $this->showCancelModal = false;

        session()->flash('success', 'Sua inscrição foi cancelada.');
    }

    public function processWalletPayment()
    {
        \Log::info('EventRegistration: Processing wallet payment', [
            'event_id' => $this->event->id,
            'event_name' => $this->event->name,
            'event_price' => $this->event->price,
            'user_wallet_balance' => $this->userWalletBalance,
            'user_id' => Auth::id(),
            'payment_method' => $this->paymentMethod
        ]);

        // Recarregar estado do componente para garantir dados atualizados
        $this->checkRegistration();

        // Se já está registrado, não processar novamente
        if ($this->isRegistered) {
            \Log::info('EventRegistration: User already registered (component state)', [
                'event_id' => $this->event->id,
                'user_id' => Auth::id(),
                'attendee_id' => $this->attendee ? $this->attendee->id : null
            ]);

            session()->flash('info', 'Você já está inscrito neste evento.');
            $this->showConfirmModal = false;
            return;
        }

        // Recarregar saldo da carteira para garantir dados atualizados
        $this->loadWalletBalance();

        // Verificar se o usuário tem carteira
        $user = Auth::user();
        if (!$user->wallet) {
            \Log::error('EventRegistration: User has no wallet', [
                'user_id' => $user->id,
                'event_id' => $this->event->id
            ]);

            session()->flash('error', 'Carteira não encontrada. Entre em contato com o suporte.');
            $this->showConfirmModal = false;
            return;
        }

        // Check if user has sufficient balance
        if ($this->userWalletBalance < $this->event->price) {
            \Log::warning('EventRegistration: Insufficient wallet balance', [
                'required' => $this->event->price,
                'available' => $this->userWalletBalance,
                'user_id' => Auth::id(),
                'wallet_id' => $user->wallet->id
            ]);

            session()->flash('error', 'Saldo insuficiente na carteira. Saldo atual: R$ ' . number_format($this->userWalletBalance, 2, ',', '.') . '. Necessário: R$ ' . number_format($this->event->price, 2, ',', '.'));
            $this->showConfirmModal = false;
            return;
        }

        try {
            // Usar transação para garantir atomicidade
            $attendee = DB::transaction(function () {
                // Verificar novamente se o usuário já está inscrito (race condition)
                $existingRegistration = EventAttendee::where('event_id', $this->event->id)
                    ->where('user_id', Auth::id())
                    ->where('status', '!=', 'cancelled')
                    ->lockForUpdate() // Lock para evitar race condition
                    ->first();

                if ($existingRegistration) {
                    \Log::warning('EventRegistration: User already registered (race condition)', [
                        'event_id' => $this->event->id,
                        'user_id' => Auth::id(),
                        'existing_attendee_id' => $existingRegistration->id
                    ]);

                    throw new \Exception('Você já está inscrito neste evento.');
                }

                // Tentar criar a inscrição com tratamento de erro de constraint
                try {
                    $attendee = EventAttendee::create([
                        'event_id' => $this->event->id,
                        'user_id' => Auth::id(),
                        'status' => 'confirmed',
                        'ticket_code' => EventAttendee::generateTicketCode(),
                        'payment_status' => 'completed',
                        'payment_method' => 'wallet',
                        'amount_paid' => $this->event->price,
                        'paid_at' => now(),
                    ]);

                    return $attendee;

                } catch (\Illuminate\Database\QueryException $e) {
                    // Se for erro de constraint de unicidade
                    if ($e->getCode() == 23000 || strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        \Log::warning('EventRegistration: Duplicate entry caught', [
                            'event_id' => $this->event->id,
                            'user_id' => Auth::id(),
                            'error' => $e->getMessage()
                        ]);

                        throw new \Exception('Você já está inscrito neste evento.');
                    }

                    // Re-throw outros erros
                    throw $e;
                }
            });

            \Log::info('EventRegistration: Attendee created for wallet payment', [
                'attendee_id' => $attendee->id,
                'ticket_code' => $attendee->ticket_code,
                'amount_paid' => $attendee->amount_paid
            ]);

            // Deduct amount from wallet
            $transaction = $user->wallet->subtractFunds(
                $this->event->price,
                'purchase',
                "Ingresso para evento: {$this->event->name}",
                $attendee->id,
                'event_registration'
            );

            if (!$transaction) {
                \Log::error('EventRegistration: Failed to deduct from wallet', [
                    'attendee_id' => $attendee->id,
                    'amount' => $this->event->price,
                    'user_id' => Auth::id(),
                    'wallet_id' => $user->wallet->id,
                    'wallet_balance' => $user->wallet->balance
                ]);

                // Delete the attendee record if wallet deduction failed
                $attendee->delete();

                session()->flash('error', 'Erro ao processar pagamento com carteira. Verifique seu saldo e tente novamente.');
                $this->showConfirmModal = false;
                return;
            }

            \Log::info('EventRegistration: Wallet payment completed', [
                'attendee_id' => $attendee->id,
                'transaction_id' => $transaction->id,
                'amount' => $this->event->price,
                'user_id' => Auth::id(),
                'new_wallet_balance' => $user->wallet->balance
            ]);

            // Update component state
            $this->attendee = $attendee;
            $this->isRegistered = true;
            $this->showConfirmModal = false;

            // Reload wallet balance
            $this->loadWalletBalance();

            // Dispatch success notification
            $this->dispatch('notify', [
                'message' => 'Inscrição realizada com sucesso! Pagamento processado via carteira. Código: ' . $attendee->ticket_code,
                'type' => 'success'
            ]);

            session()->flash('success', 'Inscrição realizada com sucesso! Pagamento processado via carteira. Código: ' . $attendee->ticket_code);

        } catch (\Exception $e) {
            // Se for erro de usuário já inscrito, tratar de forma diferente
            if (strpos($e->getMessage(), 'já está inscrito') !== false) {
                \Log::info('EventRegistration: User already registered handled', [
                    'event_id' => $this->event->id,
                    'user_id' => Auth::id(),
                    'message' => $e->getMessage()
                ]);

                // Atualizar estado do componente
                $this->checkRegistration();

                // Dispatch info notification
                $this->dispatch('notify', [
                    'message' => $e->getMessage(),
                    'type' => 'info'
                ]);

                session()->flash('info', $e->getMessage());
                $this->showConfirmModal = false;
                return;
            }

            \Log::error('EventRegistration: Error processing wallet payment', [
                'error' => $e->getMessage(),
                'event_id' => $this->event->id,
                'user_id' => Auth::id(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Dispatch error notification
            $this->dispatch('notify', [
                'message' => 'Erro ao processar pagamento com carteira: ' . $e->getMessage(),
                'type' => 'error'
            ]);

            session()->flash('error', 'Erro ao processar pagamento com carteira: ' . $e->getMessage());
            $this->showConfirmModal = false;
        }
    }
}
