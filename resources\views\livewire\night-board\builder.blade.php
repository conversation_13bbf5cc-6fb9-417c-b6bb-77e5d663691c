<div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 p-4">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-title mb-2">Monte Sua Noite</h1>
            <p class="text-body">Crie um tabuleiro interativo com suas experiências da noite e permita que outros patrocinem cada etapa!</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Blocos Temáticos -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4">
                    <h3 class="text-lg font-semibold text-title mb-4">Blocos Temáticos</h3>
                    <div class="space-y-3">
                        @foreach($this->blockTypes as $type => $block)
                            @if(is_array($block) && isset($block['name'], $block['icon'], $block['color']))
                            <div
                                draggable="true"
                                data-block-type="{{ $type }}"
                                data-block-color="{{ $block['color'] }}"
                                wire:click="selectBlock('{{ $type }}')"
                                class="night-board-block w-full p-3 rounded-lg border-2 cursor-grab active:cursor-grabbing {{ $selectedBlock === $type ? 'selected border-zinc-400 dark:border-zinc-500 bg-zinc-100 dark:bg-zinc-700' : 'border-zinc-200 dark:border-zinc-600 hover:border-zinc-300 dark:hover:border-zinc-500' }}"
                                style="box-shadow: {{ $selectedBlock === $type ? '0 0 15px ' . $block['color'] . '50' : 'none' }}"
                                x-data
                                x-on:dragstart="
                                    $event.dataTransfer.setData('text/plain', '{{ $type }}');
                                    $event.dataTransfer.effectAllowed = 'copy';
                                    $el.classList.add('dragging');

                                    // Criar imagem de drag personalizada
                                    const dragImage = $el.cloneNode(true);
                                    dragImage.style.transform = 'rotate(5deg) scale(0.8)';
                                    dragImage.style.opacity = '0.8';
                                    document.body.appendChild(dragImage);
                                    $event.dataTransfer.setDragImage(dragImage, 50, 25);
                                    setTimeout(() => document.body.removeChild(dragImage), 0);
                                "
                                x-on:dragend="
                                    $el.classList.remove('dragging');
                                "
                            >
                                <div class="flex items-center space-x-3">
                                    <flux:icon
                                        name="{{ $block['icon'] }}"
                                        class="block-icon w-6 h-6"
                                        style="color: {{ $block['color'] }};"
                                    />
                                    <span class="text-sm font-medium text-title">{{ $block['name'] }}</span>
                                </div>
                            </div>
                            @endif
                        @endforeach
                    </div>

                    <!-- Controles -->
                    <div class="mt-6 space-y-3">
                        <flux:button
                            wire:click="clearBoard"
                            variant="ghost"
                            size="sm"
                            class="w-full"
                        >
                            <flux:icon name="trash" class="w-4 h-4 mr-2" />
                            Limpar Tabuleiro
                        </flux:button>

                        <flux:button
                            wire:click="openSaveModal"
                            variant="primary"
                            size="sm"
                            class="night-board-btn w-full neon-box-red"
                        >
                            <flux:icon name="bookmark" class="w-4 h-4 mr-2" />
                            Salvar Plano
                        </flux:button>
                    </div>
                </div>
            </div>

            <!-- Tabuleiro -->
            <div class="lg:col-span-3">
                <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">
                    <h3 class="text-lg font-semibold text-title mb-4">Tabuleiro 8x8</h3>
                    <p class="text-sm text-body mb-4">
                        <span class="font-medium">Casas Pretas:</span> Arraste seus blocos aqui |
                        <span class="font-medium">Casas Brancas:</span> Espaços para patrocínio
                    </p>

                    <div class="grid grid-cols-8 gap-1 max-w-2xl mx-auto">
                        @if(is_array($board))
                        @foreach(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] as $row)
                            @for($col = 1; $col <= 8; $col++)
                                @php
                                    $position = (string) ($row . $col);
                                    $square = $board[$position] ?? ['type' => null, 'is_black' => false, 'custom_text' => ''];
                                    $isBlack = $square['is_black'] ?? false;
                                @endphp

                                <div
                                    wire:click="placeBlock('{{ $position }}')"
                                    data-position="{{ $position }}"
                                    data-is-black="{{ $isBlack ? 'true' : 'false' }}"
                                    class="chess-square aspect-square border border-zinc-300 dark:border-zinc-600 rounded-lg cursor-pointer relative {{ $isBlack ? 'bg-zinc-800 dark:bg-zinc-900' : 'bg-zinc-100 dark:bg-zinc-200' }} {{ $selectedBlock && $isBlack ? 'drag-over' : '' }}"
                                    x-data
                                    x-on:dragover.prevent="
                                        if ($el.dataset.isBlack === 'true') {
                                            $event.dataTransfer.dropEffect = 'copy';
                                            $el.classList.add('drag-over');
                                        } else {
                                            $event.dataTransfer.dropEffect = 'none';
                                            $el.classList.add('drag-invalid');
                                        }
                                    "
                                    x-on:dragleave="
                                        $el.classList.remove('drag-over', 'drag-invalid');
                                    "
                                    x-on:drop.prevent="
                                        $el.classList.remove('drag-over', 'drag-invalid');
                                        if ($el.dataset.isBlack === 'true') {
                                            const blockType = $event.dataTransfer.getData('text/plain');
                                            const position = $el.dataset.position;
                                            if (blockType && position) {
                                                // Adicionar efeito de sucesso
                                                $el.style.animation = 'bounce 0.5s ease-in-out';
                                                setTimeout(() => $el.style.animation = '', 500);
                                                $wire.placeBlockDrop(position, blockType);
                                            }
                                        }
                                    "
                                >
                                    <!-- Posição -->
                                    <div class="absolute top-1 left-1 text-xs font-mono {{ $isBlack ? 'text-zinc-400' : 'text-zinc-600' }}">
                                        {{ $position }}
                                    </div>

                                    <!-- Conteúdo da casa -->
                                    <div class="flex items-center justify-center h-full">
                                        @if(isset($square['type']) && $square['type'] && isset($this->blockTypes[$square['type']]))
                                            <div class="text-center">
                                                <flux:icon
                                                    name="{{ $this->blockTypes[$square['type']]['icon'] }}"
                                                    class="block-icon w-6 h-6 mx-auto mb-1"
                                                    style="color: {{ $this->blockTypes[$square['type']]['color'] }};"
                                                />
                                                <div class="text-xs text-zinc-300 font-medium">
                                                    {{ $this->blockTypes[$square['type']]['name'] }}
                                                </div>
                                            </div>

                                            <!-- Botão de remover -->
                                            <button
                                                wire:click.stop="removeBlock('{{ $position }}')"
                                                class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs transition-all duration-200"
                                            >
                                                ×
                                            </button>
                                        @elseif($isBlack && $selectedBlock && isset($this->blockTypes[$selectedBlock]))
                                            <div class="text-center opacity-50">
                                                <flux:icon
                                                    name="{{ $this->blockTypes[$selectedBlock]['icon'] }}"
                                                    class="w-6 h-6 mx-auto"
                                                    style="color: {{ $this->blockTypes[$selectedBlock]['color'] }};"
                                                />
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endfor
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Salvar -->
    <flux:modal wire:model="showSaveModal" class="max-w-md">
        <flux:modal.header>
            <flux:heading size="lg">Salvar Plano da Noite</flux:heading>
        </flux:modal.header>

        <flux:modal.body>
            <div class="space-y-4">
                <flux:field>
                    <flux:label>Título do Plano</flux:label>
                    <flux:input wire:model="title" placeholder="Ex: Noite Romântica no Centro" />
                    <flux:error name="title" />
                </flux:field>

                <flux:field>
                    <flux:label>Descrição (opcional)</flux:label>
                    <flux:textarea wire:model="description" placeholder="Descreva seu plano da noite..." rows="3" />
                    <flux:error name="description" />
                </flux:field>

                <flux:field>
                    <flux:checkbox wire:model="is_public">
                        Tornar público (outros usuários poderão ver e patrocinar)
                    </flux:checkbox>
                </flux:field>
            </div>
        </flux:modal.body>

        <flux:modal.footer>
            <flux:button wire:click="$set('showSaveModal', false)" variant="ghost">
                Cancelar
            </flux:button>
            <flux:button wire:click="savePlan" variant="primary">
                Salvar Plano
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
</div>
