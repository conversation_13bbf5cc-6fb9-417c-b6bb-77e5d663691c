<?php

// Teste específico para eventos pagos
require_once 'vendor/autoload.php';

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;

// Configurar Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testando eventos pagos...\n\n";

try {
    // 1. Criar ou encontrar um evento pago
    echo "1. Verificando eventos pagos...\n";
    
    $paidEvent = Event::where('is_active', true)
        ->where('date', '>=', now()->toDateString())
        ->where('price', '>', 0)
        ->first();
    
    if (!$paidEvent) {
        echo "   ⚠️  Criando evento pago de teste...\n";
        $paidEvent = Event::create([
            'name' => 'Evento Pago de Teste',
            'slug' => 'evento-pago-teste-' . time(),
            'description' => 'Evento criado para teste de pagamentos',
            'date' => now()->addDays(7)->toDateString(),
            'start_time' => now()->addDays(7)->setTime(20, 0),
            'end_time' => now()->addDays(7)->setTime(23, 0),
            'price' => 50.00, // Paid event
            'capacity' => 100,
            'location' => 'Local de Teste',
            'is_active' => true,
            'is_featured' => false,
            'created_by' => 1,
        ]);
        echo "   ✅ Evento pago criado: {$paidEvent->name} - {$paidEvent->formatted_price}\n";
    } else {
        echo "   ✅ Evento pago encontrado: {$paidEvent->name} - {$paidEvent->formatted_price}\n";
    }

    // 2. Verificar propriedades do evento
    echo "\n2. Verificando propriedades do evento pago...\n";
    echo "   - Nome: {$paidEvent->name}\n";
    echo "   - Preço: {$paidEvent->formatted_price}\n";
    echo "   - É gratuito: " . ($paidEvent->is_free ? 'Sim' : 'Não') . "\n";
    echo "   - Preço numérico: R$ " . number_format($paidEvent->price, 2, ',', '.') . "\n";
    echo "   - Ativo: " . ($paidEvent->is_active ? 'Sim' : 'Não') . "\n";
    echo "   - Data: {$paidEvent->formatted_date}\n";

    // 3. Verificar configuração do Stripe
    echo "\n3. Verificando configuração do Stripe...\n";
    $stripeKey = config('cashier.key');
    $stripeSecret = config('cashier.secret');
    
    echo "   - Stripe Key: " . (empty($stripeKey) ? '❌ Não configurado' : '✅ Configurado (' . substr($stripeKey, 0, 10) . '...)') . "\n";
    echo "   - Stripe Secret: " . (empty($stripeSecret) ? '❌ Não configurado' : '✅ Configurado (' . substr($stripeSecret, 0, 10) . '...)') . "\n";

    // 4. Verificar se há usuários para teste
    echo "\n4. Verificando usuários...\n";
    $user = User::where('role', '!=', 'visitante')->first();
    
    if (!$user) {
        echo "   ❌ Nenhum usuário encontrado\n";
        exit(1);
    }
    
    echo "   ✅ Usuário encontrado: {$user->name} (ID: {$user->id})\n";

    // 5. Verificar se usuário já está inscrito
    echo "\n5. Verificando inscrição existente...\n";
    $existingRegistration = EventAttendee::where('event_id', $paidEvent->id)
        ->where('user_id', $user->id)
        ->where('status', '!=', 'cancelled')
        ->first();

    if ($existingRegistration) {
        echo "   ⚠️  Usuário já está inscrito (Status: {$existingRegistration->status}, Pagamento: {$existingRegistration->payment_status})\n";
        echo "   🧹 Cancelando inscrição para teste...\n";
        $existingRegistration->update(['status' => 'cancelled']);
    } else {
        echo "   ✅ Usuário não está inscrito\n";
    }

    // 6. Simular criação de inscrição pendente (como faria o Livewire)
    echo "\n6. Simulando criação de inscrição pendente...\n";
    
    $pendingAttendee = EventAttendee::create([
        'event_id' => $paidEvent->id,
        'user_id' => $user->id,
        'status' => 'registered',
        'payment_status' => 'pending',
    ]);

    echo "   ✅ Inscrição pendente criada (ID: {$pendingAttendee->id})\n";

    // 7. Testar criação de sessão Stripe (simulado)
    echo "\n7. Testando configuração Stripe...\n";
    
    if (empty($stripeSecret)) {
        echo "   ❌ Stripe Secret não configurado - não é possível testar pagamento\n";
    } else {
        try {
            // Configurar Stripe
            \Stripe\Stripe::setApiKey($stripeSecret);
            
            // Tentar criar uma sessão de teste (sem salvar)
            $sessionData = [
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'brl',
                        'product_data' => [
                            'name' => $paidEvent->name,
                            'description' => "Ingresso para {$paidEvent->name} em {$paidEvent->formatted_date}",
                        ],
                        'unit_amount' => (int)($paidEvent->price * 100), // Convert to cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => 'https://example.com/success',
                'cancel_url' => 'https://example.com/cancel',
                'customer_email' => $user->email,
                'metadata' => [
                    'event_id' => $paidEvent->id,
                    'attendee_id' => $pendingAttendee->id,
                    'user_id' => $user->id,
                ],
            ];
            
            echo "   ✅ Dados da sessão Stripe preparados:\n";
            echo "      - Produto: {$sessionData['line_items'][0]['price_data']['product_data']['name']}\n";
            echo "      - Valor: R$ " . number_format($sessionData['line_items'][0]['price_data']['unit_amount'] / 100, 2, ',', '.') . "\n";
            echo "      - Email: {$sessionData['customer_email']}\n";
            echo "      - Metadata: " . json_encode($sessionData['metadata']) . "\n";
            
            // Tentar criar a sessão real (comentado para não gastar créditos)
            // $session = \Stripe\Checkout\Session::create($sessionData);
            // echo "   ✅ Sessão Stripe criada: {$session->id}\n";
            // echo "   🔗 URL de pagamento: {$session->url}\n";
            
            echo "   ✅ Configuração Stripe está funcionando\n";
            
        } catch (\Exception $e) {
            echo "   ❌ Erro ao configurar Stripe: {$e->getMessage()}\n";
        }
    }

    // 8. Limpar dados de teste
    echo "\n8. Limpando dados de teste...\n";
    $pendingAttendee->delete();
    echo "   ✅ Inscrição pendente removida\n";

    echo "\n✅ Teste de evento pago concluído!\n";

    // 9. Mostrar instruções
    echo "\n📋 Instruções para testar no browser:\n";
    echo "   1. Acesse: /eventos/{$paidEvent->slug}\n";
    echo "   2. Clique em 'Inscrever-se por {$paidEvent->formatted_price}'\n";
    echo "   3. Confirme a inscrição\n";
    echo "   4. Deve ser redirecionado para o Stripe\n";
    echo "   5. Verifique os logs em storage/logs/laravel.log\n";

} catch (Exception $e) {
    echo "\n❌ Erro durante o teste: {$e->getMessage()}\n";
    echo "📍 Arquivo: {$e->getFile()}:{$e->getLine()}\n";
    exit(1);
}

echo "\n🎉 Teste concluído!\n";
