APP_NAME=Desiree
APP_ENV=production
APP_KEY=base64:RE1xry7thKq5kxBKCPfGoyxxSXqcwJe82DH12oexRSM=
APP_DEBUG=false
APP_URL=https://www.swingcuritiba.com.br/public
APP_LOCALE=pt_BR
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
#APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=daily
# LOG_STACK=single  # Commenting out as we're using daily driver directly
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Banco de dados configurado na KingHost
DB_CONNECTION=mysql
DB_HOST=mysql.swingcuritiba.com.br
DB_PORT=3306
DB_DATABASE=desiree
DB_USERNAME=desiree
DB_PASSWORD=GodTei45cva

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=public
QUEUE_CONNECTION=sync

CACHE_STORE=database
# CACHE_PREFIX=head

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.kinghost.net
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="GodTei45!cva"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
MAIL_ENCRYPTION=tls

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

STRIPE_KEY=pk_live_XubwAelZx3SOqlbGpra0hsud00L0ly1jfU
STRIPE_SECRET=******************************************
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
