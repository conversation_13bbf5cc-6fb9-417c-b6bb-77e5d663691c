<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class VisitorTimeLimit
{
    /**
     * Handle an incoming request.
     * 
     * Controla o limite de tempo diário para usuários visitantes (10 minutos)
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Só aplica o limite para usuários visitantes
        if ($user->role !== 'visitante') {
            return $next($request);
        }

        $now = Carbon::now();
        $today = $now->format('Y-m-d');

        // Se é um novo dia, reseta o contador
        if (!$user->daily_access_start || $user->daily_access_start->format('Y-m-d') !== $today) {
            $user->update([
                'daily_access_start' => $now,
                'daily_minutes_used' => 0
            ]);
        }

        // Calcula o tempo usado hoje
        $accessStart = Carbon::parse($user->daily_access_start);
        $minutesUsed = $user->daily_minutes_used;

        // Se já passou de 10 minutos, redireciona para home
        if ($minutesUsed >= 10) {
            // Se está tentando acessar a home, permite
            if ($request->routeIs('home')) {
                return $next($request);
            }

            // Se está tentando fazer logout, permite
            if ($request->routeIs('logout')) {
                return $next($request);
            }

            // Redireciona para home com mensagem
            return redirect()->route('home')->with('warning', 'Seu tempo diário de acesso (10 minutos) foi esgotado. Volte amanhã ou considere fazer upgrade para VIP!');
        }

        return $next($request);
    }
}
