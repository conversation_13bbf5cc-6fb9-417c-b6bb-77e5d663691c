<?php

namespace App\Livewire\NightBoard;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\NightBoardPlan;

class PlanList extends Component
{
    use WithPagination;

    public $search = '';
    public $sortBy = 'recent'; // recent, popular, supported

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function setSortBy($sort)
    {
        $this->sortBy = $sort;
        $this->resetPage();
    }

    public function render()
    {
        $query = NightBoardPlan::query()
            ->with(['user', 'supports'])
            ->public();

        // Filtro de busca
        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhereHas('user', function($userQuery) {
                      $userQuery->where('name', 'like', '%' . $this->search . '%');
                  });
            });
        }

        // Ordenação
        switch ($this->sortBy) {
            case 'popular':
                $query->withCount('supports')->orderByDesc('supports_count');
                break;
            case 'supported':
                $query->mostSupported();
                break;
            default: // recent
                $query->latest();
                break;
        }

        $plans = $query->paginate(12);

        return view('livewire.night-board.plan-list', compact('plans'));
    }
}
