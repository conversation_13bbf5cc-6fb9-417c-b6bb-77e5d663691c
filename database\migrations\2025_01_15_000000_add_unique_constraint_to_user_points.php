<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, clean up any duplicate records by keeping only the latest one for each user
        $duplicateUsers = DB::table('user_points')
            ->select('user_id', DB::raw('COUNT(*) as count'))
            ->groupBy('user_id')
            ->having('count', '>', 1)
            ->get();

        foreach ($duplicateUsers as $duplicate) {
            // Get all records for this user, ordered by created_at desc
            $userPoints = DB::table('user_points')
                ->where('user_id', $duplicate->user_id)
                ->orderBy('created_at', 'desc')
                ->get();

            // Keep the first (latest) record and merge data from others
            $latestRecord = $userPoints->first();
            $totalPoints = $userPoints->sum('total_points');
            $dailyPoints = $userPoints->sum('daily_points');
            $weeklyPoints = $userPoints->sum('weekly_points');
            $monthlyPoints = $userPoints->sum('monthly_points');
            $maxStreakDays = $userPoints->max('streak_days');

            // Update the latest record with merged data
            DB::table('user_points')
                ->where('id', $latestRecord->id)
                ->update([
                    'total_points' => $totalPoints,
                    'daily_points' => $dailyPoints,
                    'weekly_points' => $weeklyPoints,
                    'monthly_points' => $monthlyPoints,
                    'streak_days' => $maxStreakDays,
                    'updated_at' => now(),
                ]);

            // Delete all other records for this user
            DB::table('user_points')
                ->where('user_id', $duplicate->user_id)
                ->where('id', '!=', $latestRecord->id)
                ->delete();
        }

        // Now add the unique constraint
        Schema::table('user_points', function (Blueprint $table) {
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_points', function (Blueprint $table) {
            $table->dropUnique(['user_id']);
        });
    }
};
