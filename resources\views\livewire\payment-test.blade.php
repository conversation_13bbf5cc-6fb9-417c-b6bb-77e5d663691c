<div class="max-w-4xl mx-auto p-6 bg-zinc-900 text-white rounded-lg">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-white mb-2">Teste do Sistema de Pagamentos</h2>
        <p class="text-gray-300">Diagnóstico e teste dos componentes de pagamento</p>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> de Teste -->
    <div class="mb-6 space-x-4">
        <button
            wire:click="runTests"
            wire:loading.attr="disabled"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
        >
            <span wire:loading.remove wire:target="runTests">Executar Todos os Testes</span>
            <span wire:loading wire:target="runTests">Executando...</span>
        </button>

        <button
            wire:click="testCardError"
            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
        >
            Simular Erro de Cartão
        </button>

        <button
            wire:click="testVipCheckout"
            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
        >
            Testar Checkout VIP
        </button>

        <button
            wire:click="clearResults"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
        >
            Limpar Resultados
        </button>
    </div>

    <!-- Resultados dos Testes -->
    @if(count($testResults) > 0)
        <div class="space-y-4">
            <h3 class="text-xl font-semibold text-white mb-4">Resultados dos Testes</h3>

            @foreach($testResults as $result)
                <div class="border rounded-lg p-4
                    @if($result['status'] === 'success') border-green-600 bg-green-900/20
                    @else border-red-600 bg-red-900/20
                    @endif">

                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold text-lg">{{ $result['test'] }}</h4>
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($result['status'] === 'success') bg-green-600 text-white
                            @else bg-red-600 text-white
                            @endif">
                            {{ $result['status'] === 'success' ? 'Sucesso' : 'Erro' }}
                        </span>
                    </div>

                    <p class="text-gray-300 mb-2">{{ $result['message'] }}</p>

                    @if(!empty($result['details']))
                        <div class="mt-3 p-3 bg-zinc-800 rounded">
                            <h5 class="font-medium text-sm text-gray-400 mb-2">Detalhes:</h5>
                            <pre class="text-xs text-gray-300 overflow-x-auto">{{ json_encode($result['details'], JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @endif

    <!-- Informações do Sistema -->
    <div class="mt-8 p-4 bg-zinc-800 rounded-lg">
        <h3 class="text-lg font-semibold text-white mb-3">Informações do Sistema</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-400">Ambiente:</span>
                <span class="text-white ml-2">{{ config('app.env') }}</span>
            </div>
            <div>
                <span class="text-gray-400">Debug:</span>
                <span class="text-white ml-2">{{ config('app.debug') ? 'Ativado' : 'Desativado' }}</span>
            </div>
            <div>
                <span class="text-gray-400">Moeda Padrão:</span>
                <span class="text-white ml-2">{{ strtoupper(config('cashier.currency', 'brl')) }}</span>
            </div>
            <div>
                <span class="text-gray-400">Locale da Moeda:</span>
                <span class="text-white ml-2">{{ config('cashier.currency_locale', 'pt_BR') }}</span>
            </div>
        </div>
    </div>

    <!-- Instruções -->
    <div class="mt-6 p-4 bg-blue-900/20 border border-blue-600 rounded-lg">
        <h3 class="text-lg font-semibold text-blue-300 mb-2">Instruções</h3>
        <ul class="text-blue-200 text-sm space-y-1">
            <li>• Execute "Todos os Testes" para verificar a configuração geral</li>
            <li>• Use "Simular Erro de Cartão" para testar as notificações de erro</li>
            <li>• Verifique os logs em storage/logs para mais detalhes</li>
            <li>• As notificações devem aparecer no canto inferior direito</li>
        </ul>
    </div>

    <!-- Link para teste específico -->
    <div class="mt-6 p-4 bg-purple-900/20 border border-purple-600 rounded-lg">
        <h3 class="text-lg font-semibold text-purple-300 mb-2">Teste Específico VIP</h3>
        <p class="text-purple-200 text-sm mb-3">Para um teste mais detalhado do sistema de checkout VIP:</p>
        <a href="{{ route('test.vip.checkout') }}"
           class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
            🧪 Teste Detalhado do Checkout VIP
        </a>
    </div>
</div>
