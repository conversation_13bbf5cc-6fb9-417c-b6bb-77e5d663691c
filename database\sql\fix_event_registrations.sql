-- SQL para corrigir problemas com inscrições de eventos
-- Execute este script no phpMyAdmin em produção

-- 1. Verificar e corrigir estrutura da tabela event_attendees
ALTER TABLE `event_attendees` 
MODIFY COLUMN `status` ENUM('registered', 'confirmed', 'cancelled', 'checked_in') NOT NULL DEFAULT 'registered',
MODIFY COLUMN `payment_status` ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
MODIFY COLUMN `payment_method` VARCHAR(50) NULL,
MODIFY COLUMN `payment_id` VARCHAR(255) NULL,
MODIFY COLUMN `amount_paid` DECIMAL(10,2) NULL DEFAULT 0.00,
MODIFY COLUMN `paid_at` TIMESTAMP NULL,
MODIFY COLUMN `checked_in_at` TIMESTAMP NULL;

-- 2. Adicionar índices para melhor performance
ALTER TABLE `event_attendees` 
ADD INDEX `idx_event_user` (`event_id`, `user_id`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_payment_status` (`payment_status`),
ADD INDEX `idx_payment_id` (`payment_id`);

-- 3. Verificar e corrigir estrutura da tabela events
ALTER TABLE `events` 
MODIFY COLUMN `is_active` BOOLEAN NOT NULL DEFAULT 1,
MODIFY COLUMN `is_featured` BOOLEAN NOT NULL DEFAULT 0,
MODIFY COLUMN `price` DECIMAL(10,2) NULL DEFAULT 0.00,
MODIFY COLUMN `capacity` INT NULL;

-- 4. Limpar registros órfãos ou inconsistentes
DELETE ea FROM event_attendees ea 
LEFT JOIN events e ON ea.event_id = e.id 
WHERE e.id IS NULL;

DELETE ea FROM event_attendees ea 
LEFT JOIN users u ON ea.user_id = u.id 
WHERE u.id IS NULL;

-- 5. Corrigir registros com status inconsistente
UPDATE event_attendees 
SET status = 'confirmed', payment_status = 'completed' 
WHERE payment_status = 'completed' AND status = 'registered';

-- 6. Gerar códigos de ingresso para registros que não possuem
UPDATE event_attendees 
SET ticket_code = CONCAT('TKT-', UPPER(SUBSTRING(MD5(CONCAT(id, event_id, user_id, NOW())), 1, 8)))
WHERE ticket_code IS NULL OR ticket_code = '';

-- 7. Verificar eventos com datas passadas e desativar se necessário
UPDATE events 
SET is_active = 0 
WHERE date < CURDATE() AND is_active = 1;

-- 8. Consultas para verificar a integridade dos dados
-- Execute estas consultas para verificar se tudo está correto:

-- Verificar eventos ativos
SELECT id, name, date, is_active, price, capacity 
FROM events 
WHERE is_active = 1 
ORDER BY date ASC;

-- Verificar inscrições por evento
SELECT 
    e.name as evento,
    e.date as data_evento,
    COUNT(ea.id) as total_inscricoes,
    COUNT(CASE WHEN ea.status = 'confirmed' THEN 1 END) as confirmadas,
    COUNT(CASE WHEN ea.payment_status = 'completed' THEN 1 END) as pagas,
    COUNT(CASE WHEN ea.status = 'cancelled' THEN 1 END) as canceladas
FROM events e
LEFT JOIN event_attendees ea ON e.id = ea.event_id
WHERE e.is_active = 1
GROUP BY e.id, e.name, e.date
ORDER BY e.date ASC;

-- Verificar registros com problemas
SELECT 
    ea.id,
    ea.event_id,
    ea.user_id,
    ea.status,
    ea.payment_status,
    ea.ticket_code,
    e.name as evento_nome,
    u.name as usuario_nome
FROM event_attendees ea
JOIN events e ON ea.event_id = e.id
JOIN users u ON ea.user_id = u.id
WHERE 
    (ea.payment_status = 'completed' AND ea.status != 'confirmed') OR
    (ea.ticket_code IS NULL OR ea.ticket_code = '') OR
    (ea.payment_status = 'pending' AND ea.paid_at IS NOT NULL)
ORDER BY ea.created_at DESC;

-- 9. Verificar duplicatas de inscrição
SELECT 
    event_id,
    user_id,
    COUNT(*) as total
FROM event_attendees 
WHERE status != 'cancelled'
GROUP BY event_id, user_id
HAVING COUNT(*) > 1;
