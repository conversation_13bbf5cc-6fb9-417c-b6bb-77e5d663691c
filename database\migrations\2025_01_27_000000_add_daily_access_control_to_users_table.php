<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'daily_access_start')) {
                $table->timestamp('daily_access_start')->nullable()->after('last_seen');
            }
            if (!Schema::hasColumn('users', 'daily_minutes_used')) {
                $table->integer('daily_minutes_used')->default(0)->after('daily_access_start');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $columns = [];
            if (Schema::hasColumn('users', 'daily_access_start')) $columns[] = 'daily_access_start';
            if (Schema::hasColumn('users', 'daily_minutes_used')) $columns[] = 'daily_minutes_used';

            if (!empty($columns)) {
                $table->dropColumn($columns);
            }
        });
    }
};
