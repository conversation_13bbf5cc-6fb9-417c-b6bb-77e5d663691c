<?php

namespace App\Console\Commands;

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TestEventRegistration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:test-registration {--user-id= : ID do usuário para teste} {--event-id= : ID do evento para teste}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test event registration functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testando funcionalidade de inscrição em eventos...');
        $this->line('');

        // Get test user
        $userId = $this->option('user-id');
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("Usuário com ID {$userId} não encontrado.");
                return 1;
            }
        } else {
            $user = User::where('role', '!=', 'visitante')->first();
            if (!$user) {
                $this->error('Nenhum usuário encontrado para teste.');
                return 1;
            }
        }

        $this->info("👤 Usuário de teste: {$user->name} (ID: {$user->id})");

        // Get test event
        $eventId = $this->option('event-id');
        if ($eventId) {
            $event = Event::find($eventId);
            if (!$event) {
                $this->error("Evento com ID {$eventId} não encontrado.");
                return 1;
            }
        } else {
            $event = Event::where('is_active', true)
                ->where('date', '>=', now()->toDateString())
                ->first();
            
            if (!$event) {
                // Create a test event
                $event = Event::create([
                    'name' => 'Evento de Teste',
                    'slug' => 'evento-de-teste-' . time(),
                    'description' => 'Evento criado para teste de inscrições',
                    'date' => now()->addDays(7)->toDateString(),
                    'start_time' => now()->addDays(7)->setTime(20, 0),
                    'end_time' => now()->addDays(7)->setTime(23, 0),
                    'price' => 0, // Free event
                    'capacity' => 100,
                    'location' => 'Local de Teste',
                    'is_active' => true,
                    'is_featured' => false,
                    'created_by' => $user->id,
                ]);
                $this->info("📅 Evento de teste criado: {$event->name} (ID: {$event->id})");
            }
        }

        $this->info("📅 Evento de teste: {$event->name} (ID: {$event->id})");
        $this->line('');

        // Test 1: Check if user is already registered
        $this->info('🔍 Teste 1: Verificando se usuário já está inscrito...');
        $existingRegistration = EventAttendee::where('event_id', $event->id)
            ->where('user_id', $user->id)
            ->where('status', '!=', 'cancelled')
            ->first();

        if ($existingRegistration) {
            $this->warn("   ⚠️  Usuário já está inscrito (Status: {$existingRegistration->status})");
            
            if ($this->confirm('Cancelar inscrição existente para continuar o teste?')) {
                $existingRegistration->update(['status' => 'cancelled']);
                $this->info('   ✅ Inscrição cancelada');
            } else {
                $this->info('Teste interrompido.');
                return 0;
            }
        } else {
            $this->info('   ✅ Usuário não está inscrito');
        }

        // Test 2: Check event validation
        $this->info('🔍 Teste 2: Verificando validações do evento...');
        
        $validations = [
            'Evento ativo' => $event->is_active,
            'Data futura' => !$event->has_passed,
            'Não esgotado' => !$event->is_sold_out,
        ];

        foreach ($validations as $check => $result) {
            if ($result) {
                $this->info("   ✅ {$check}");
            } else {
                $this->error("   ❌ {$check}");
                return 1;
            }
        }

        // Test 3: Test registration creation
        $this->info('🔍 Teste 3: Testando criação de inscrição...');
        
        try {
            DB::beginTransaction();

            $attendee = EventAttendee::create([
                'event_id' => $event->id,
                'user_id' => $user->id,
                'status' => 'confirmed',
                'ticket_code' => EventAttendee::generateTicketCode(),
                'payment_status' => 'completed',
                'payment_method' => 'free',
                'amount_paid' => 0,
                'paid_at' => now(),
            ]);

            $this->info("   ✅ Inscrição criada com sucesso (ID: {$attendee->id})");
            $this->info("   🎫 Código do ingresso: {$attendee->ticket_code}");

            // Test 4: Verify registration
            $this->info('🔍 Teste 4: Verificando inscrição criada...');
            
            $verifyAttendee = EventAttendee::where('event_id', $event->id)
                ->where('user_id', $user->id)
                ->where('status', '!=', 'cancelled')
                ->first();

            if ($verifyAttendee) {
                $this->info('   ✅ Inscrição encontrada no banco de dados');
                $this->info("   📊 Status: {$verifyAttendee->status}");
                $this->info("   💳 Status do pagamento: {$verifyAttendee->payment_status}");
                $this->info("   🎫 Código: {$verifyAttendee->ticket_code}");
            } else {
                $this->error('   ❌ Inscrição não encontrada no banco de dados');
                DB::rollBack();
                return 1;
            }

            // Test 5: Test duplicate prevention
            $this->info('🔍 Teste 5: Testando prevenção de duplicatas...');
            
            try {
                $duplicateAttendee = EventAttendee::create([
                    'event_id' => $event->id,
                    'user_id' => $user->id,
                    'status' => 'confirmed',
                    'ticket_code' => EventAttendee::generateTicketCode(),
                    'payment_status' => 'completed',
                    'payment_method' => 'free',
                    'amount_paid' => 0,
                    'paid_at' => now(),
                ]);
                
                $this->warn('   ⚠️  Duplicata criada - isso não deveria acontecer');
            } catch (\Exception $e) {
                $this->info('   ✅ Duplicata prevenida (como esperado)');
            }

            DB::rollBack(); // Rollback to clean up test data
            $this->info('🧹 Dados de teste removidos');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("   ❌ Erro ao criar inscrição: {$e->getMessage()}");
            $this->error("   📍 Arquivo: {$e->getFile()}:{$e->getLine()}");
            return 1;
        }

        $this->line('');
        $this->info('✅ Todos os testes passaram! A funcionalidade de inscrição está funcionando.');
        
        // Show current statistics
        $this->showStatistics();

        return 0;
    }

    private function showStatistics()
    {
        $this->line('');
        $this->info('📊 Estatísticas atuais:');
        
        $activeEvents = Event::where('is_active', true)->count();
        $totalRegistrations = EventAttendee::where('status', '!=', 'cancelled')->count();
        $confirmedRegistrations = EventAttendee::where('status', 'confirmed')->count();
        $pendingRegistrations = EventAttendee::where('payment_status', 'pending')->count();

        $this->table(
            ['Métrica', 'Valor'],
            [
                ['Eventos ativos', $activeEvents],
                ['Total de inscrições', $totalRegistrations],
                ['Inscrições confirmadas', $confirmedRegistrations],
                ['Pagamentos pendentes', $pendingRegistrations],
            ]
        );
    }
}
