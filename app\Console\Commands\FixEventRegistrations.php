<?php

namespace App\Console\Commands;

use App\Models\Event;
use App\Models\EventAttendee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixEventRegistrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:fix-registrations {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix common issues with event registrations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 Running in DRY RUN mode - no changes will be made');
        } else {
            $this->info('🔧 Fixing event registration issues...');
        }

        $this->line('');

        // 1. Fix inconsistent payment status
        $this->fixInconsistentPaymentStatus($dryRun);

        // 2. Generate missing ticket codes
        $this->generateMissingTicketCodes($dryRun);

        // 3. Remove duplicate registrations
        $this->removeDuplicateRegistrations($dryRun);

        // 4. Clean orphaned registrations
        $this->cleanOrphanedRegistrations($dryRun);

        // 5. Deactivate past events
        $this->deactivatePastEvents($dryRun);

        $this->line('');
        $this->info('✅ Event registration fix completed!');

        // Show summary
        $this->showSummary();
    }

    private function fixInconsistentPaymentStatus($dryRun)
    {
        $this->info('1. Fixing inconsistent payment status...');

        $inconsistent = EventAttendee::where('payment_status', 'completed')
            ->where('status', 'registered')
            ->get();

        if ($inconsistent->count() > 0) {
            $this->warn("   Found {$inconsistent->count()} registrations with completed payment but registered status");
            
            if (!$dryRun) {
                EventAttendee::where('payment_status', 'completed')
                    ->where('status', 'registered')
                    ->update(['status' => 'confirmed']);
                $this->info("   ✅ Fixed {$inconsistent->count()} registrations");
            }
        } else {
            $this->info('   ✅ No inconsistent payment status found');
        }
    }

    private function generateMissingTicketCodes($dryRun)
    {
        $this->info('2. Generating missing ticket codes...');

        $missingCodes = EventAttendee::where(function($query) {
            $query->whereNull('ticket_code')
                  ->orWhere('ticket_code', '');
        })->where('status', '!=', 'cancelled')->get();

        if ($missingCodes->count() > 0) {
            $this->warn("   Found {$missingCodes->count()} registrations without ticket codes");
            
            if (!$dryRun) {
                foreach ($missingCodes as $attendee) {
                    $attendee->update([
                        'ticket_code' => EventAttendee::generateTicketCode()
                    ]);
                }
                $this->info("   ✅ Generated {$missingCodes->count()} ticket codes");
            }
        } else {
            $this->info('   ✅ All registrations have ticket codes');
        }
    }

    private function removeDuplicateRegistrations($dryRun)
    {
        $this->info('3. Checking for duplicate registrations...');

        $duplicates = DB::table('event_attendees')
            ->select('event_id', 'user_id', DB::raw('COUNT(*) as count'))
            ->where('status', '!=', 'cancelled')
            ->groupBy('event_id', 'user_id')
            ->having('count', '>', 1)
            ->get();

        if ($duplicates->count() > 0) {
            $this->warn("   Found {$duplicates->count()} duplicate registrations");
            
            if (!$dryRun) {
                foreach ($duplicates as $duplicate) {
                    // Keep the most recent registration, cancel others
                    $attendees = EventAttendee::where('event_id', $duplicate->event_id)
                        ->where('user_id', $duplicate->user_id)
                        ->where('status', '!=', 'cancelled')
                        ->orderBy('created_at', 'desc')
                        ->get();

                    // Keep the first (most recent), cancel the rest
                    $attendees->skip(1)->each(function($attendee) {
                        $attendee->update(['status' => 'cancelled']);
                    });
                }
                $this->info("   ✅ Removed duplicate registrations");
            }
        } else {
            $this->info('   ✅ No duplicate registrations found');
        }
    }

    private function cleanOrphanedRegistrations($dryRun)
    {
        $this->info('4. Cleaning orphaned registrations...');

        // Find registrations for non-existent events
        $orphanedEvents = EventAttendee::whereNotIn('event_id', Event::pluck('id'))->get();
        
        // Find registrations for non-existent users
        $orphanedUsers = EventAttendee::whereNotIn('user_id', DB::table('users')->pluck('id'))->get();

        $totalOrphaned = $orphanedEvents->count() + $orphanedUsers->count();

        if ($totalOrphaned > 0) {
            $this->warn("   Found {$totalOrphaned} orphaned registrations");
            
            if (!$dryRun) {
                $orphanedEvents->each->delete();
                $orphanedUsers->each->delete();
                $this->info("   ✅ Cleaned {$totalOrphaned} orphaned registrations");
            }
        } else {
            $this->info('   ✅ No orphaned registrations found');
        }
    }

    private function deactivatePastEvents($dryRun)
    {
        $this->info('5. Deactivating past events...');

        $pastEvents = Event::where('date', '<', now()->toDateString())
            ->where('is_active', true)
            ->get();

        if ($pastEvents->count() > 0) {
            $this->warn("   Found {$pastEvents->count()} past events still active");
            
            if (!$dryRun) {
                Event::where('date', '<', now()->toDateString())
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
                $this->info("   ✅ Deactivated {$pastEvents->count()} past events");
            }
        } else {
            $this->info('   ✅ No past events to deactivate');
        }
    }

    private function showSummary()
    {
        $this->info('📊 Current Statistics:');
        
        $activeEvents = Event::where('is_active', true)->count();
        $totalRegistrations = EventAttendee::where('status', '!=', 'cancelled')->count();
        $confirmedRegistrations = EventAttendee::where('status', 'confirmed')->count();
        $pendingPayments = EventAttendee::where('payment_status', 'pending')->count();

        $this->table(
            ['Metric', 'Count'],
            [
                ['Active Events', $activeEvents],
                ['Total Registrations', $totalRegistrations],
                ['Confirmed Registrations', $confirmedRegistrations],
                ['Pending Payments', $pendingPayments],
            ]
        );
    }
}
