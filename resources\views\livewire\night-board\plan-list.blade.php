<div class="min-h-screen bg-zinc-50 dark:bg-zinc-900 p-4">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-title mb-2">Monte Sua Noite</h1>
                    <p class="text-body">Explore planos de noite criados pela comunidade e patrocine suas experiências favoritas!</p>
                </div>

                <flux:button
                    href="{{ route('night-board.create') }}"
                    variant="primary"
                    class="night-board-btn neon-box-red"
                    wire:navigate
                >
                    <flux:icon name="plus" class="w-4 h-4 mr-2" />
                    Criar Meu Plano
                </flux:button>
            </div>
        </div>

        <!-- Filtros e Busca -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-4 mb-6">
            <div class="flex flex-col md:flex-row gap-4">
                <!-- Busca -->
                <div class="flex-1">
                    <flux:input
                        wire:model.live.debounce.300ms="search"
                        placeholder="Buscar planos por título, descrição ou criador..."
                        class="w-full"
                    >
                        <x-slot name="iconTrailing">
                            <flux:icon name="magnifying-glass" class="w-4 h-4" />
                        </x-slot>
                    </flux:input>
                </div>

                <!-- Ordenação -->
                <div class="flex gap-2">
                    <flux:button
                        wire:click="setSortBy('recent')"
                        variant="{{ $sortBy === 'recent' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        Recentes
                    </flux:button>
                    <flux:button
                        wire:click="setSortBy('popular')"
                        variant="{{ $sortBy === 'popular' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        Populares
                    </flux:button>
                    <flux:button
                        wire:click="setSortBy('supported')"
                        variant="{{ $sortBy === 'supported' ? 'primary' : 'ghost' }}"
                        size="sm"
                    >
                        Mais Patrocinados
                    </flux:button>
                </div>
            </div>
        </div>

        <!-- Grid de Planos -->
        @if($plans->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                @foreach($plans as $plan)
                    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 overflow-hidden hover:shadow-lg transition-all duration-300 neon-box hover:scale-105">
                        <!-- Preview do Tabuleiro -->
                        <div class="p-4 bg-zinc-50 dark:bg-zinc-700">
                            <div class="board-preview grid grid-cols-8 gap-0.5 max-w-32 mx-auto">
                                @if(is_array($plan->board))
                                @foreach(['A', 'B', 'C', 'D'] as $row)
                                    @for($col = 1; $col <= 8; $col++)
                                        @php
                                            $position = (string) ($row . $col);
                                            $square = $plan->board[$position] ?? ['type' => null];
                                            $rowIndex = ord($row) - ord('A');
                                            $isBlack = ($rowIndex + $col) % 2 === 0;
                                        @endphp

                                        <div class="aspect-square {{ $isBlack ? 'bg-zinc-800' : 'bg-zinc-200' }} rounded-sm flex items-center justify-center">
                                            @if(isset($square['type']) && $square['type'] && $isBlack)
                                                <div class="w-1 h-1 rounded-full bg-green-400"></div>
                                            @elseif(!$isBlack && $plan->hasSupportAtPosition($position))
                                                <div class="w-1 h-1 rounded-full bg-yellow-400"></div>
                                            @endif
                                        </div>
                                    @endfor
                                @endforeach
                                @endif
                            </div>
                        </div>

                        <!-- Conteúdo -->
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-title mb-2 line-clamp-2">{{ $plan->title }}</h3>

                            @if($plan->description)
                                <p class="text-sm text-body mb-3 line-clamp-2">{{ $plan->description }}</p>
                            @endif

                            <!-- Informações -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-xs text-body">
                                    <flux:icon name="user" class="w-3 h-3 mr-1" />
                                    <span>{{ $plan->user->name }}</span>
                                </div>

                                <div class="flex items-center justify-between text-xs">
                                    <div class="flex items-center text-body">
                                        <flux:icon name="calendar" class="w-3 h-3 mr-1" />
                                        <span>{{ $plan->created_at->format('d/m/Y') }}</span>
                                    </div>

                                    <div class="flex items-center text-green-600 dark:text-green-400">
                                        <flux:icon name="currency-dollar" class="w-3 h-3 mr-1" />
                                        <span class="font-semibold">R$ {{ number_format($plan->total_support_amount, 2, ',', '.') }}</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-xs text-body">
                                    <span>{{ $plan->supports->count() }} patrocínios</span>
                                    <span>{{ collect($plan->board)->where('type', '!=', null)->count() }} experiências</span>
                                </div>
                            </div>

                            <!-- Ações -->
                            <flux:button
                                href="{{ route('night-board.show', $plan->id) }}"
                                variant="primary"
                                size="sm"
                                class="night-board-btn w-full"
                                wire:navigate
                            >
                                Ver Plano
                            </flux:button>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Paginação -->
            <div class="flex justify-center">
                {{ $plans->links() }}
            </div>
        @else
            <!-- Estado Vazio -->
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <flux:icon name="puzzle-piece" class="w-16 h-16 mx-auto text-zinc-400 mb-4" />
                    <h3 class="text-lg font-semibold text-title mb-2">Nenhum plano encontrado</h3>
                    <p class="text-body mb-6">
                        @if($search)
                            Não encontramos planos que correspondam à sua busca.
                        @else
                            Seja o primeiro a criar um plano da noite!
                        @endif
                    </p>
                    <flux:button
                        href="{{ route('night-board.create') }}"
                        variant="primary"
                        wire:navigate
                    >
                        <flux:icon name="plus" class="w-4 h-4 mr-2" />
                        Criar Primeiro Plano
                    </flux:button>
                </div>
            </div>
        @endif
    </div>
</div>
