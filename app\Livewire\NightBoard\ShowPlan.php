<?php

namespace App\Livewire\NightBoard;

use Livewire\Component;
use App\Models\NightBoardPlan;
use App\Models\NightBoardSupport;
use Livewire\Attributes\Validate;

class ShowPlan extends Component
{
    public NightBoardPlan $plan;
    public $showSponsorModal = false;
    public $selectedPosition = null;

    #[Validate('required|numeric|min:1|max:1000')]
    public $sponsorAmount = '';

    #[Validate('nullable|string|max:500')]
    public $sponsorComment = '';

    // Tipos de blocos (mesmo do Builder)
    public function getBlockTypesProperty()
    {
        return [
            'ambiente' => [
                'name' => 'Ambiente',
                'icon' => 'cube-transparent',
                'color' => '#E60073',
            ],
            'bebida' => [
                'name' => 'Bebida',
                'icon' => 'beaker',
                'color' => '#00FFF7',
            ],
            'musica' => [
                'name' => 'Música',
                'icon' => 'musical-note',
                'color' => '#FFE600',
            ],
            'acao_fisica' => [
                'name' => 'Ação Física',
                'icon' => 'fire',
                'color' => '#FF6B35',
            ],
            'acao_virtual' => [
                'name' => 'Ação Virtual',
                'icon' => 'device-phone-mobile',
                'color' => '#9D4EDD',
            ],
            'desafio' => [
                'name' => 'Desafio',
                'icon' => 'puzzle-piece',
                'color' => '#06FFA5',
            ],
        ];
    }

    public function mount(NightBoardPlan $plan)
    {
        $this->plan = $plan->load(['user', 'supports.user']);
    }

    public function openSponsorModal($position)
    {
        // Verifica se a posição é uma casa branca
        if ($this->isBlackSquare($position)) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'Você só pode patrocinar casas brancas!'
            ]);
            return;
        }

        // Verifica se já existe patrocínio nesta posição
        if ($this->plan->hasSupportAtPosition($position)) {
            $this->dispatch('toast', [
                'type' => 'info',
                'message' => 'Esta posição já foi patrocinada!'
            ]);
            return;
        }

        $this->selectedPosition = $position;
        $this->sponsorAmount = '';
        $this->sponsorComment = '';
        $this->showSponsorModal = true;
    }

    public function sponsorPosition()
    {
        $this->validate();

        // Verifica se o usuário tem saldo suficiente na carteira
        $userWallet = auth()->user()->wallet;
        if ($userWallet->balance < $this->sponsorAmount) {
            $this->dispatch('toast', [
                'type' => 'error',
                'message' => 'Saldo insuficiente na carteira!'
            ]);
            return;
        }

        // Cria o patrocínio
        NightBoardSupport::create([
            'user_id' => auth()->id(),
            'plan_id' => $this->plan->id,
            'position' => $this->selectedPosition,
            'amount' => $this->sponsorAmount,
            'comment' => $this->sponsorComment,
        ]);

        // Debita da carteira do patrocinador usando o método subtractFunds
        $userWallet->subtractFunds(
            $this->sponsorAmount,
            'transfer_out',
            "Patrocínio no plano '" . (string) $this->plan->title . "' - Posição " . (string) $this->selectedPosition
        );

        // Credita na carteira do criador do plano usando o método addFunds
        $this->plan->user->wallet->addFunds(
            $this->sponsorAmount,
            'transfer_in',
            "Patrocínio recebido no plano '" . (string) $this->plan->title . "' - Posição " . (string) $this->selectedPosition,
            null,
            'night_board_support',
            auth()->id()
        );

        $this->showSponsorModal = false;
        $this->plan->refresh();

        $this->dispatch('toast', [
            'type' => 'success',
            'message' => 'Patrocínio realizado com sucesso!'
        ]);
    }

    public function isBlackSquare($position)
    {
        $row = substr($position, 0, 1);
        $col = (int) substr($position, 1);
        $rowIndex = ord($row) - ord('A');
        return ($rowIndex + $col) % 2 === 0;
    }

    public function getSupportAtPosition($position)
    {
        return $this->plan->getSupportAtPosition($position);
    }

    public function render()
    {
        return view('livewire.night-board.show-plan');
    }
}
