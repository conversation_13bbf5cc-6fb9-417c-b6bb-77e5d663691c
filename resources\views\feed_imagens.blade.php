<?php
use Illuminate\Support\Facades\Storage;
?>

<x-layouts.app :title="__('Ultimas Imagens')">
    <div class="bg-white dark:bg-zinc-700 min-h-screen py-6 sm:py-8 lg:py-12">
        <div class="mx-auto max-w-screen-2xl px-4 md:px-8">

            @if($posts->count() > 0)
                <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:gap-6 xl:gap-8">
                    @foreach ($posts as $post)
                        @if($post->image)
                            <!-- image - start -->
                            <a href="{{ route('post.show', ['post' => $post->id]) }}"
                                class="group relative flex h-48 items-end overflow-hidden rounded-lg bg-gray-100 shadow-lg md:h-80">
                                <img src="{{ Storage::url($post->image) }}" loading="lazy" alt="Image from post" class="absolute inset-0 h-full w-full object-cover object-center transition duration-200 group-hover:scale-110" />

                                <div
                                    class="pointer-events-none absolute inset-0 bg-gradient-to-t from-gray-800 via-transparent to-transparent opacity-50">
                                </div>

                                <div class="relative ml-4 mb-3 md:ml-5">
                                    @if($post->user)
                                        <p class="text-xs text-white/80 mb-1">{{ $post->user->name }}</p>
                                    @endif
                                    @if($post->content)
                                        <span class="inline-block text-sm text-white md:text-lg line-clamp-2">{{ Str::limit($post->content, 100) }}</span>
                                    @endif
                                </div>
                            </a>
                            <!-- image - end -->
                        @endif
                    @endforeach
                </div>
            @else
                <div class="flex flex-col items-center justify-center py-12">
                    <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Nenhuma imagem encontrada</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Ainda não há posts com imagens para exibir.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-layouts.app>
