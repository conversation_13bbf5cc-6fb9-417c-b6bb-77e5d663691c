<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class VisitorTimer extends Component
{
    public $timeRemaining = 0; // em segundos
    public $isVisible = false;
    public $totalMinutes = 10;
    public $accessStartTime = null;

    public function mount()
    {
        $this->initializeTimer();
    }

    public function initializeTimer()
    {
        if (!Auth::check()) {
            $this->isVisible = false;
            return;
        }

        $user = Auth::user();

        // Só mostra para visitantes
        if ($user->role !== 'visitante') {
            $this->isVisible = false;
            return;
        }

        $this->isVisible = true;
        $now = Carbon::now();
        $today = $now->format('Y-m-d');

        // Se é um novo dia, reseta o contador
        if (!$user->daily_access_start || $user->daily_access_start->format('Y-m-d') !== $today) {
            $user->update([
                'daily_access_start' => $now,
                'daily_minutes_used' => 0
            ]);
            $this->accessStartTime = $now->timestamp;
        } else {
            $this->accessStartTime = Carbon::parse($user->daily_access_start)->timestamp;
        }

        // Calcula tempo restante em segundos
        $totalSeconds = $this->totalMinutes * 60; // 10 minutos = 600 segundos
        $secondsElapsed = $now->timestamp - $this->accessStartTime;
        $this->timeRemaining = max(0, $totalSeconds - $secondsElapsed);

        // Se o tempo acabou, redireciona
        if ($this->timeRemaining <= 0) {
            $this->redirectToHome();
        }
    }

    public function updateUsedTime()
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();

        if ($user->role !== 'visitante') {
            return;
        }

        $now = Carbon::now();

        if ($user->daily_access_start) {
            $accessStart = Carbon::parse($user->daily_access_start);
            $minutesElapsed = $now->diffInMinutes($accessStart);

            // Atualiza apenas se passou pelo menos 1 minuto completo
            if ($minutesElapsed > $user->daily_minutes_used) {
                $user->update(['daily_minutes_used' => min($minutesElapsed, $this->totalMinutes)]);
            }
        }
    }

    public function redirectToHome()
    {
        // Atualiza o banco antes de redirecionar
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->role === 'visitante' && $user->daily_access_start) {
                $now = Carbon::now();
                $accessStart = Carbon::parse($user->daily_access_start);
                $minutesElapsed = $now->diffInMinutes($accessStart);
                $user->update(['daily_minutes_used' => min($minutesElapsed, $this->totalMinutes)]);
            }
        }

        session()->flash('warning', 'Seu tempo diário de acesso (10 minutos) foi esgotado. Volte amanhã ou considere fazer upgrade para VIP!');
        $this->redirect(route('home'));
    }

    public function render()
    {
        return view('livewire.visitor-timer');
    }
}
