-- SQL para resolver problemas de integridade referencial na exclusão de produtos
-- Execute este SQL no phpMyAdmin para ajustar as restrições de chave estrangeira

-- 1. Verificar produtos que têm pedidos associados (não podem ser excluídos)
SELECT 
    p.id,
    p.name,
    p.status,
    COUNT(oi.id) as total_vendas,
    SUM(oi.quantity) as quantidade_vendida
FROM products p
INNER JOIN order_items oi ON p.id = oi.product_id
GROUP BY p.id, p.name, p.status
ORDER BY total_vendas DESC;

-- 2. Verificar produtos em carrinhos de compras
SELECT 
    p.id,
    p.name,
    p.status,
    COUNT(ci.id) as itens_carrinho
FROM products p
INNER JOIN cart_items ci ON p.id = ci.product_id
GROUP BY p.id, p.name, p.status
ORDER BY itens_carrinho DESC;

-- 3. Verificar produtos em listas de desejos
SELECT 
    p.id,
    p.name,
    p.status,
    COUNT(w.id) as itens_wishlist
FROM products p
INNER JOIN wishlists w ON p.id = w.product_id
GROUP BY p.id, p.name, p.status
ORDER BY itens_wishlist DESC;

-- 4. Desativar produtos que já foram vendidos (em vez de tentar excluí-los)
-- ATENÇÃO: Execute apenas se quiser desativar todos os produtos que já foram vendidos
-- UPDATE products 
-- SET status = 'inactive' 
-- WHERE id IN (
--     SELECT DISTINCT product_id 
--     FROM order_items
-- );

-- 5. Verificar a estrutura atual da tabela order_items
DESCRIBE order_items;

-- 6. Verificar as restrições de chave estrangeira atuais
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME,
    DELETE_RULE,
    UPDATE_RULE
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_NAME = 'products' 
AND TABLE_SCHEMA = DATABASE();

-- 7. Se necessário, você pode alterar a restrição para SET NULL em vez de RESTRICT
-- ATENÇÃO: Execute apenas se souber o que está fazendo
-- Primeiro, remover a restrição existente:
-- ALTER TABLE order_items DROP FOREIGN KEY order_items_ibfk_2;

-- Depois, recriar com SET NULL (isso permitirá que produtos sejam excluídos, 
-- mas manterá o histórico de pedidos com product_id = NULL)
-- ALTER TABLE order_items 
-- ADD CONSTRAINT order_items_product_id_foreign 
-- FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL;

-- 8. Alternativa: Adicionar uma coluna para manter informações do produto mesmo após exclusão
-- ALTER TABLE order_items ADD COLUMN product_name VARCHAR(255) AFTER product_id;
-- ALTER TABLE order_items ADD COLUMN product_price_at_time DECIMAL(10,2) AFTER product_name;

-- 9. Atualizar dados existentes com informações do produto
-- UPDATE order_items oi 
-- INNER JOIN products p ON oi.product_id = p.id 
-- SET oi.product_name = p.name, 
--     oi.product_price_at_time = oi.price 
-- WHERE oi.product_name IS NULL;

-- 10. Verificar produtos órfãos (sem categoria, imagens, etc.)
SELECT 
    p.id,
    p.name,
    p.status,
    p.category_id,
    CASE WHEN p.image IS NULL OR p.image = '' THEN 'Sem imagem' ELSE 'Com imagem' END as tem_imagem,
    CASE WHEN pi.product_id IS NULL THEN 'Sem imagens adicionais' ELSE 'Com imagens adicionais' END as tem_imagens_extras
FROM products p
LEFT JOIN product_images pi ON p.id = pi.product_id
WHERE p.category_id IS NULL OR p.image IS NULL OR p.image = ''
GROUP BY p.id, p.name, p.status, p.category_id, p.image;

-- 11. Limpar produtos órfãos que podem ser excluídos com segurança
-- (produtos sem vendas, sem carrinho, sem wishlist)
SELECT 
    p.id,
    p.name,
    p.status,
    'Pode ser excluído com segurança' as observacao
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN cart_items ci ON p.id = ci.product_id
LEFT JOIN wishlists w ON p.id = w.product_id
WHERE oi.id IS NULL 
AND ci.id IS NULL 
AND w.id IS NULL;

-- Para excluir estes produtos órfãos (CUIDADO - teste antes):
-- DELETE FROM products 
-- WHERE id IN (
--     SELECT p.id
--     FROM (
--         SELECT p.id
--         FROM products p
--         LEFT JOIN order_items oi ON p.id = oi.product_id
--         LEFT JOIN cart_items ci ON p.id = ci.product_id
--         LEFT JOIN wishlists w ON p.id = w.product_id
--         WHERE oi.id IS NULL 
--         AND ci.id IS NULL 
--         AND w.id IS NULL
--     ) as temp
-- );
