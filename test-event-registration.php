<?php

// Teste simples para verificar se as inscrições de eventos estão funcionando
require_once 'vendor/autoload.php';

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use Illuminate\Support\Facades\DB;

// Configurar Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testando funcionalidade de inscrição em eventos...\n\n";

try {
    // 1. Verificar se há eventos ativos
    echo "1. Verificando eventos ativos...\n";
    $activeEvents = Event::where('is_active', true)
        ->where('date', '>=', now()->toDateString())
        ->get();
    
    echo "   ✅ Encontrados {$activeEvents->count()} eventos ativos\n";
    
    if ($activeEvents->count() === 0) {
        echo "   ⚠️  Criando evento de teste...\n";
        $event = Event::create([
            'name' => 'Evento de Teste',
            'slug' => 'evento-de-teste-' . time(),
            'description' => 'Evento criado para teste de inscrições',
            'date' => now()->addDays(7)->toDateString(),
            'start_time' => now()->addDays(7)->setTime(20, 0),
            'end_time' => now()->addDays(7)->setTime(23, 0),
            'price' => 0, // Free event
            'capacity' => 100,
            'location' => 'Local de Teste',
            'is_active' => true,
            'is_featured' => false,
            'created_by' => 1, // Assumindo que existe um usuário com ID 1
        ]);
        echo "   ✅ Evento de teste criado: {$event->name}\n";
    } else {
        $event = $activeEvents->first();
        echo "   ✅ Usando evento existente: {$event->name}\n";
    }

    // 2. Verificar se há usuários
    echo "\n2. Verificando usuários...\n";
    $user = User::where('role', '!=', 'visitante')->first();
    
    if (!$user) {
        echo "   ❌ Nenhum usuário encontrado\n";
        exit(1);
    }
    
    echo "   ✅ Usuário encontrado: {$user->name} (ID: {$user->id})\n";

    // 3. Verificar propriedades do evento
    echo "\n3. Verificando propriedades do evento...\n";
    echo "   - É gratuito: " . ($event->is_free ? 'Sim' : 'Não') . "\n";
    echo "   - Já passou: " . ($event->has_passed ? 'Sim' : 'Não') . "\n";
    echo "   - Está esgotado: " . ($event->is_sold_out ? 'Sim' : 'Não') . "\n";
    echo "   - Vagas disponíveis: " . $event->available_spots . "\n";

    // 4. Verificar se usuário já está inscrito
    echo "\n4. Verificando inscrição existente...\n";
    $existingRegistration = EventAttendee::where('event_id', $event->id)
        ->where('user_id', $user->id)
        ->where('status', '!=', 'cancelled')
        ->first();

    if ($existingRegistration) {
        echo "   ⚠️  Usuário já está inscrito (Status: {$existingRegistration->status})\n";
        echo "   🧹 Cancelando inscrição para teste...\n";
        $existingRegistration->update(['status' => 'cancelled']);
    } else {
        echo "   ✅ Usuário não está inscrito\n";
    }

    // 5. Testar criação de inscrição
    echo "\n5. Testando criação de inscrição...\n";
    
    DB::beginTransaction();
    
    $attendee = EventAttendee::create([
        'event_id' => $event->id,
        'user_id' => $user->id,
        'status' => 'confirmed',
        'ticket_code' => EventAttendee::generateTicketCode(),
        'payment_status' => 'completed',
        'payment_method' => 'free',
        'amount_paid' => 0,
        'paid_at' => now(),
    ]);

    echo "   ✅ Inscrição criada com sucesso!\n";
    echo "   📊 ID: {$attendee->id}\n";
    echo "   🎫 Código: {$attendee->ticket_code}\n";
    echo "   📅 Status: {$attendee->status}\n";
    echo "   💳 Pagamento: {$attendee->payment_status}\n";

    // 6. Verificar se a inscrição foi salva
    echo "\n6. Verificando se a inscrição foi salva...\n";
    $verifyAttendee = EventAttendee::where('event_id', $event->id)
        ->where('user_id', $user->id)
        ->where('status', '!=', 'cancelled')
        ->first();

    if ($verifyAttendee) {
        echo "   ✅ Inscrição encontrada no banco de dados\n";
    } else {
        echo "   ❌ Inscrição não encontrada no banco de dados\n";
        DB::rollBack();
        exit(1);
    }

    // 7. Testar propriedades do evento após inscrição
    echo "\n7. Verificando propriedades após inscrição...\n";
    $event->refresh(); // Recarregar do banco
    echo "   - Vagas disponíveis: " . $event->available_spots . "\n";
    echo "   - Está esgotado: " . ($event->is_sold_out ? 'Sim' : 'Não') . "\n";

    DB::rollBack(); // Limpar dados de teste
    echo "\n🧹 Dados de teste removidos\n";

    echo "\n✅ Todos os testes passaram! A funcionalidade de inscrição está funcionando.\n";

} catch (Exception $e) {
    echo "\n❌ Erro durante o teste: {$e->getMessage()}\n";
    echo "📍 Arquivo: {$e->getFile()}:{$e->getLine()}\n";
    echo "🔍 Stack trace:\n{$e->getTraceAsString()}\n";
    exit(1);
}

// Mostrar estatísticas
echo "\n📊 Estatísticas atuais:\n";
$activeEvents = Event::where('is_active', true)->count();
$totalRegistrations = EventAttendee::where('status', '!=', 'cancelled')->count();
$confirmedRegistrations = EventAttendee::where('status', 'confirmed')->count();
$pendingRegistrations = EventAttendee::where('payment_status', 'pending')->count();

echo "   - Eventos ativos: {$activeEvents}\n";
echo "   - Total de inscrições: {$totalRegistrations}\n";
echo "   - Inscrições confirmadas: {$confirmedRegistrations}\n";
echo "   - Pagamentos pendentes: {$pendingRegistrations}\n";

echo "\n🎉 Teste concluído com sucesso!\n";
